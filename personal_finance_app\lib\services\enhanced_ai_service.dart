import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;
import '../models/transaction.dart' as app_models;
import '../models/account.dart';
import '../models/ai_recommendation.dart';

class EnhancedAIService {
  static const String _openAIApiKey = 'YOUR_OPENAI_API_KEY'; // Gerçek API key buraya
  static const String _openAIBaseUrl = 'https://api.openai.com/v1';
  
  // Şimdilik mock data kullanıyoruz, gerçek API key ile değiştirilebilir
  static const bool _useMockData = true;

  // Ana AI önerileri metodu
  Future<List<AIRecommendation>> getFinancialRecommendations({
    required List<Account> accounts,
    required List<app_models.Transaction> transactions,
  }) async {
    if (_useMockData) {
      return _generateSmartRecommendations(accounts, transactions);
    }

    try {
      final analysis = await _analyzeWithAI(accounts, transactions);
      return await _generateAIRecommendations(analysis);
    } catch (e) {
      return _generateSmartRecommendations(accounts, transactions);
    }
  }

  // Eski API uyumluluğu için
  Future<List<AIRecommendation>> getRecommendations() async {
    return getFinancialRecommendations(accounts: [], transactions: []);
  }

  // Akıllı kategori önerisi
  Future<String> suggestCategory(String description, double amount) async {
    if (_useMockData) {
      return _getSmartCategoryFromDescription(description, amount);
    }
    
    try {
      final response = await http.post(
        Uri.parse('$_openAIBaseUrl/chat/completions'),
        headers: {
          'Authorization': 'Bearer $_openAIApiKey',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'model': 'gpt-3.5-turbo',
          'messages': [
            {
              'role': 'system',
              'content': 'Sen bir finansal kategorizasyon uzmanısın. Verilen işlem açıklamasına göre en uygun kategoriyi öner. Sadece kategori adını döndür: Gıda, Ulaşım, Kira, Faturalar, Sağlık, Eğlence, Alışveriş, Maaş, Freelance, Yatırım, Hediye, Diğer'
            },
            {
              'role': 'user',
              'content': 'İşlem açıklaması: "$description", Tutar: $amount TL'
            }
          ],
          'max_tokens': 50,
          'temperature': 0.3,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['choices'][0]['message']['content'].trim();
      }
    } catch (e) {
      print('AI kategori önerisi hatası: $e');
    }
    
    return _getSmartCategoryFromDescription(description, amount);
  }

  // Harcama pattern analizi
  Future<Map<String, dynamic>> analyzeSpendingPatterns(
    List<app_models.Transaction> transactions
  ) async {
    final now = DateTime.now();
    final lastMonth = DateTime(now.year, now.month - 1, now.day);
    
    final recentTransactions = transactions.where((t) => 
      t.date.isAfter(lastMonth) && t.type == app_models.TransactionType.expense
    ).toList();
    
    // Kategori bazlı analiz
    final categorySpending = <String, double>{};
    final categoryCount = <String, int>{};
    final dailySpending = <String, double>{};
    
    for (var transaction in recentTransactions) {
      categorySpending[transaction.category] = 
        (categorySpending[transaction.category] ?? 0) + transaction.amount;
      categoryCount[transaction.category] = 
        (categoryCount[transaction.category] ?? 0) + 1;
      
      final dayKey = '${transaction.date.day}/${transaction.date.month}';
      dailySpending[dayKey] = (dailySpending[dayKey] ?? 0) + transaction.amount;
    }
    
    // En çok harcama yapılan kategoriler
    final topCategories = categorySpending.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    // Haftalık trend analizi
    final weeklySpending = <int, double>{};
    for (var transaction in recentTransactions) {
      final weekOfYear = _getWeekOfYear(transaction.date);
      weeklySpending[weekOfYear] = 
        (weeklySpending[weekOfYear] ?? 0) + transaction.amount;
    }
    
    // Anormal harcama tespiti
    final averageDaily = dailySpending.values.isEmpty ? 0 : 
      dailySpending.values.reduce((a, b) => a + b) / dailySpending.length;
    final anomalies = dailySpending.entries.where((e) => 
      e.value > averageDaily * 2
    ).toList();
    
    return {
      'totalSpent': recentTransactions.fold(0.0, (sum, t) => sum + t.amount),
      'transactionCount': recentTransactions.length,
      'topCategories': topCategories.take(5).map((e) => {
        'category': e.key,
        'amount': e.value,
        'count': categoryCount[e.key] ?? 0,
        'percentage': (e.value / recentTransactions.fold(0.0, (sum, t) => sum + t.amount)) * 100,
      }).toList(),
      'weeklyTrend': weeklySpending,
      'dailyAverage': averageDaily,
      'anomalies': anomalies.map((e) => {
        'date': e.key,
        'amount': e.value,
        'multiplier': e.value / averageDaily,
      }).toList(),
      'averagePerTransaction': recentTransactions.isEmpty ? 0 : 
        recentTransactions.fold(0.0, (sum, t) => sum + t.amount) / recentTransactions.length,
    };
  }

  // Akıllı bütçe önerisi
  Future<Map<String, double>> suggestSmartBudget({
    required double monthlyIncome,
    required List<app_models.Transaction> transactions,
  }) async {
    final analysis = await analyzeSpendingPatterns(transactions);
    final topCategories = analysis['topCategories'] as List;
    
    // Gelir bazlı akıllı bütçe hesaplama
    final budget = <String, double>{};
    
    // Temel ihtiyaçlar (gelirin %50'si)
    final needs = monthlyIncome * 0.5;
    budget['Kira'] = needs * 0.35;
    budget['Gıda'] = needs * 0.25;
    budget['Ulaşım'] = needs * 0.20;
    budget['Faturalar'] = needs * 0.20;
    
    // İstekler (gelirin %30'u)
    final wants = monthlyIncome * 0.3;
    budget['Eğlence'] = wants * 0.35;
    budget['Alışveriş'] = wants * 0.30;
    budget['Sağlık'] = wants * 0.20;
    budget['Hediye'] = wants * 0.15;
    
    // Birikim (gelirin %20'si)
    budget['Birikim'] = monthlyIncome * 0.2;
    
    // Mevcut harcama verilerine göre ayarlama
    for (var category in topCategories) {
      final categoryName = category['category'] as String;
      final currentSpending = category['amount'] as double;
      
      if (budget.containsKey(categoryName)) {
        // Eğer mevcut harcama önerilen bütçeden çok fazlaysa, uyarı ver
        if (currentSpending > budget[categoryName]! * 1.5) {
          budget['${categoryName}_warning'] = currentSpending - budget[categoryName]!;
        }
      }
    }
    
    return budget;
  }

  // Finansal sağlık skoru
  Future<Map<String, dynamic>> calculateFinancialHealthScore({
    required List<Account> accounts,
    required List<app_models.Transaction> transactions,
    required double monthlyIncome,
  }) async {
    final totalBalance = accounts.fold(0.0, (sum, account) => sum + account.balance);
    final monthlyExpense = transactions
      .where((t) => t.type == app_models.TransactionType.expense)
      .fold(0.0, (sum, t) => sum + t.amount);
    
    var score = 0.0;
    final factors = <String, dynamic>{};
    
    // Acil durum fonu (25 puan)
    final emergencyFundRatio = totalBalance / (monthlyExpense * 3);
    final emergencyScore = (emergencyFundRatio * 25).clamp(0, 25);
    score += emergencyScore;
    factors['emergencyFund'] = {
      'score': emergencyScore,
      'ratio': emergencyFundRatio,
      'description': emergencyFundRatio >= 1 ? 'Mükemmel' : 
                   emergencyFundRatio >= 0.5 ? 'İyi' : 'Geliştirilmeli'
    };
    
    // Gelir-gider dengesi (25 puan)
    final savingsRatio = (monthlyIncome - monthlyExpense) / monthlyIncome;
    final savingsScore = (savingsRatio * 25 * 4).clamp(0, 25); // %25 birikim ideal
    score += savingsScore;
    factors['savings'] = {
      'score': savingsScore,
      'ratio': savingsRatio,
      'description': savingsRatio >= 0.2 ? 'Mükemmel' : 
                   savingsRatio >= 0.1 ? 'İyi' : 'Geliştirilmeli'
    };
    
    // Çeşitlilik (25 puan)
    final diversityScore = accounts.length >= 3 ? 25 : accounts.length * 8.33;
    score += diversityScore;
    factors['diversity'] = {
      'score': diversityScore,
      'accountCount': accounts.length,
      'description': accounts.length >= 3 ? 'Mükemmel' : 
                   accounts.length >= 2 ? 'İyi' : 'Geliştirilmeli'
    };
    
    // Harcama disiplini (25 puan)
    final analysis = await analyzeSpendingPatterns(transactions);
    final anomalies = analysis['anomalies'] as List;
    final disciplineScore = (25 - (anomalies.length * 5)).clamp(0, 25);
    score += disciplineScore;
    factors['discipline'] = {
      'score': disciplineScore,
      'anomalies': anomalies.length,
      'description': anomalies.length <= 2 ? 'Mükemmel' : 
                   anomalies.length <= 5 ? 'İyi' : 'Geliştirilmeli'
    };
    
    return {
      'totalScore': score.clamp(0, 100),
      'grade': _getGrade(score),
      'factors': factors,
      'recommendations': _getScoreBasedRecommendations(score, factors),
    };
  }

  // Akıllı öneriler üretimi
  List<AIRecommendation> _generateSmartRecommendations(
    List<Account> accounts, 
    List<app_models.Transaction> transactions
  ) {
    final recommendations = <AIRecommendation>[];
    final totalBalance = accounts.fold(0.0, (sum, account) => sum + account.balance);
    final monthlyExpense = transactions
      .where((t) => t.type == app_models.TransactionType.expense)
      .fold(0.0, (sum, t) => sum + t.amount);
    final monthlyIncome = transactions
      .where((t) => t.type == app_models.TransactionType.income)
      .fold(0.0, (sum, t) => sum + t.amount);
    
    // 1. Acil Durum Fonu Analizi
    final emergencyFundNeeded = monthlyExpense * 6;
    if (totalBalance < emergencyFundNeeded) {
      final deficit = emergencyFundNeeded - totalBalance;
      recommendations.add(AIRecommendation(
        id: 'emergency_fund',
        title: '🚨 Acil Durum Fonu Eksik',
        description: 'Finansal güvenliğiniz için ${deficit.toStringAsFixed(0)} TL daha biriktirmeniz gerekiyor. Bu, 6 aylık giderinizi karşılayacak acil durum fonu için gerekli.',
        category: 'Tasarruf',
        priority: 'high',
        actionable: true,
        estimatedImpact: 'Finansal güvenliğinizi %90 artırır',
        createdAt: DateTime.now(),
      ));
    } else {
      recommendations.add(AIRecommendation(
        id: 'emergency_fund_complete',
        title: '✅ Acil Durum Fonunuz Hazır!',
        description: 'Tebrikler! 6 aylık giderinizi karşılayacak acil durum fonunuz mevcut. Artık yatırım fırsatlarını değerlendirebilirsiniz.',
        category: 'Başarı',
        priority: 'low',
        actionable: false,
        estimatedImpact: 'Finansal güvenliğiniz mükemmel',
        createdAt: DateTime.now(),
      ));
    }
    
    // 2. Harcama Analizi
    final categorySpending = <String, double>{};
    for (var transaction in transactions.where((t) => t.type == app_models.TransactionType.expense)) {
      categorySpending[transaction.category] = 
        (categorySpending[transaction.category] ?? 0) + transaction.amount;
    }
    
    final topSpendingCategory = categorySpending.entries.isNotEmpty 
      ? categorySpending.entries.reduce((a, b) => a.value > b.value ? a : b)
      : null;
    
    if (topSpendingCategory != null && topSpendingCategory.value > monthlyIncome * 0.3) {
      recommendations.add(AIRecommendation(
        id: 'spending_optimization',
        title: '💰 ${topSpendingCategory.key} Harcamalarını Optimize Edin',
        description: '${topSpendingCategory.key} kategorisinde aylık ${topSpendingCategory.value.toStringAsFixed(0)} TL harcıyorsunuz. Bu, gelirinizin %${((topSpendingCategory.value / monthlyIncome) * 100).toStringAsFixed(0)}\'i. Hedef %25\'in altında olmalı.',
        category: 'Harcama',
        priority: 'medium',
        actionable: true,
        estimatedImpact: 'Aylık ${(topSpendingCategory.value * 0.2).toStringAsFixed(0)} TL tasarruf',
        createdAt: DateTime.now(),
      ));
    }
    
    return recommendations;
  }

  String _getSmartCategoryFromDescription(String description, double amount) {
    final desc = description.toLowerCase();
    
    // Akıllı kategorizasyon - tutar ve açıklama kombinasyonu
    if (desc.contains('market') || desc.contains('gıda') || desc.contains('yemek') || 
        desc.contains('restaurant') || desc.contains('cafe') || desc.contains('migros') ||
        desc.contains('carrefour') || desc.contains('bim') || desc.contains('a101')) {
      return 'Gıda';
    } else if (desc.contains('benzin') || desc.contains('otobüs') || desc.contains('metro') ||
               desc.contains('taksi') || desc.contains('uber') || desc.contains('bitaksi') ||
               desc.contains('dolmuş') || desc.contains('park')) {
      return 'Ulaşım';
    } else if (desc.contains('kira') || desc.contains('ev') || desc.contains('apartman') ||
               desc.contains('aidat') || (amount > 1000 && desc.contains('ödeme'))) {
      return 'Kira';
    } else if (desc.contains('elektrik') || desc.contains('su') || desc.contains('doğalgaz') ||
               desc.contains('internet') || desc.contains('telefon') || desc.contains('fatura')) {
      return 'Faturalar';
    } else if (desc.contains('hastane') || desc.contains('doktor') || desc.contains('eczane') ||
               desc.contains('sağlık') || desc.contains('ilaç')) {
      return 'Sağlık';
    } else if (desc.contains('sinema') || desc.contains('konser') || desc.contains('eğlence') ||
               desc.contains('oyun') || desc.contains('netflix') || desc.contains('spotify')) {
      return 'Eğlence';
    } else if (desc.contains('maaş') || desc.contains('salary') || desc.contains('gelir') ||
               (amount > 3000 && desc.contains('transfer'))) {
      return 'Maaş';
    } else if (desc.contains('freelance') || desc.contains('proje') || desc.contains('danışmanlık') ||
               desc.contains('serbest')) {
      return 'Freelance';
    } else if (desc.contains('yatırım') || desc.contains('hisse') || desc.contains('borsa') ||
               desc.contains('altın') || desc.contains('döviz')) {
      return 'Yatırım';
    } else if (desc.contains('hediye') || desc.contains('gift') || desc.contains('doğum günü')) {
      return 'Hediye';
    } else {
      return 'Diğer';
    }
  }

  String _getGrade(double score) {
    if (score >= 90) return 'A+';
    if (score >= 80) return 'A';
    if (score >= 70) return 'B+';
    if (score >= 60) return 'B';
    if (score >= 50) return 'C+';
    if (score >= 40) return 'C';
    return 'D';
  }

  List<String> _getScoreBasedRecommendations(double score, Map<String, dynamic> factors) {
    final recommendations = <String>[];
    
    if (score < 50) {
      recommendations.add('Acil durum fonu oluşturmaya öncelik verin');
      recommendations.add('Aylık harcamalarınızı gözden geçirin');
      recommendations.add('Bütçe planı oluşturun');
    } else if (score < 75) {
      recommendations.add('Mevcut birikimlerinizi artırın');
      recommendations.add('Yatırım seçeneklerini araştırın');
      recommendations.add('Harcama kategorilerini optimize edin');
    } else {
      recommendations.add('Portföy çeşitlendirmesi yapın');
      recommendations.add('Uzun vadeli yatırım planları oluşturun');
      recommendations.add('Vergi avantajlı yatırım araçlarını değerlendirin');
    }
    
    return recommendations;
  }

  int _getWeekOfYear(DateTime date) {
    final firstDayOfYear = DateTime(date.year, 1, 1);
    final daysSinceFirstDay = date.difference(firstDayOfYear).inDays;
    return (daysSinceFirstDay / 7).ceil();
  }

  Future<Map<String, dynamic>> _analyzeWithAI(
    List<Account> accounts, 
    List<app_models.Transaction> transactions
  ) async {
    // Gerçek AI analizi için placeholder
    return {};
  }

  Future<List<AIRecommendation>> _generateAIRecommendations(
    Map<String, dynamic> analysis
  ) async {
    // Gerçek AI önerileri için placeholder
    return [];
  }
}
