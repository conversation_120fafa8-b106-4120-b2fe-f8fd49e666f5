import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../models/transaction.dart' as app_models;
import '../../models/account.dart';
import '../../services/auth_service.dart';
import '../../services/database_service.dart';

import 'add_transaction_screen.dart';
import '../analytics/analytics_screen.dart';

class TransactionsScreen extends StatefulWidget {
  const TransactionsScreen({super.key});

  @override
  State<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends State<TransactionsScreen> with TickerProviderStateMixin {
  final DatabaseService _databaseService = DatabaseService();
  List<app_models.Transaction> _transactions = [];
  List<Account> _accounts = [];
  bool _isLoading = true;
  String _selectedFilter = 'all'; // all, income, expense
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final user = authService.currentUser;

      if (user != null) {
        final transactions = await _databaseService.getTransactionsByUserId(user.id!);
        final accounts = await _databaseService.getAccountsByUserId(user.id!);

        setState(() {
          _transactions = transactions;
          _accounts = accounts;
        });
      }
    } catch (e) {
      // Error loading transactions - silently handle
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<app_models.Transaction> get _filteredTransactions {
    switch (_selectedFilter) {
      case 'income':
        return _transactions.where((t) => t.type == app_models.TransactionType.income).toList();
      case 'expense':
        return _transactions.where((t) => t.type == app_models.TransactionType.expense).toList();
      default:
        return _transactions;
    }
  }

  Future<void> _deleteTransaction(app_models.Transaction transaction) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('İşlemi Sil'),
        content: const Text('Bu işlemi silmek istediğinizden emin misiniz?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('İptal'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Sil'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _databaseService.deleteTransaction(transaction.id!);
        _loadData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('İşlem başarıyla silindi')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('İşlem silinirken hata oluştu')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'Harcamalar',
          style: TextStyle(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics_outlined),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AnalyticsScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.blue,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Colors.blue,
          onTap: (index) {
            setState(() {
              switch (index) {
                case 0:
                  _selectedFilter = 'all';
                  break;
                case 1:
                  _selectedFilter = 'income';
                  break;
                case 2:
                  _selectedFilter = 'expense';
                  break;
              }
            });
          },
          tabs: const [
            Tab(text: 'Tümü'),
            Tab(text: 'Gelir'),
            Tab(text: 'Gider'),
          ],
        ),
      ),
      body: _isLoading ? _buildLoadingView() : _buildTransactionsView(),
      floatingActionButton: FloatingActionButton(
        heroTag: "add_transaction",
        onPressed: () async {
          final result = await Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => AddTransactionScreen(accounts: _accounts),
            ),
          );
          if (result == true) {
            _loadData();
          }
        },
        backgroundColor: Colors.blue,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildLoadingView() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildTransactionsView() {
    final filteredTransactions = _filteredTransactions;

    if (filteredTransactions.isEmpty) {
      return _buildEmptyView();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredTransactions.length,
      itemBuilder: (context, index) {
        return _buildTransactionCard(filteredTransactions[index]);
      },
    );
  }

  Widget _buildEmptyView() {
    String message;
    String subtitle;
    IconData icon;

    switch (_selectedFilter) {
      case 'income':
        message = 'Henüz gelir kaydı yok';
        subtitle = 'İlk gelir kaydınızı eklemek için + butonuna tıklayın';
        icon = Icons.trending_up;
        break;
      case 'expense':
        message = 'Henüz gider kaydı yok';
        subtitle = 'İlk gider kaydınızı eklemek için + butonuna tıklayın';
        icon = Icons.trending_down;
        break;
      default:
        message = 'Henüz işlem kaydı yok';
        subtitle = 'İlk işleminizi eklemek için + butonuna tıklayın';
        icon = Icons.receipt_long;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionCard(app_models.Transaction transaction) {
    final formatter = NumberFormat.currency(locale: 'tr_TR', symbol: '₺');
    final dateFormatter = DateFormat('dd MMM yyyy', 'tr_TR');

    final account = _accounts.firstWhere(
      (a) => a.id == transaction.accountId,
      orElse: () => Account(
        userId: 0,
        bankName: 'Bilinmeyen',
        accountName: 'Hesap',
        balance: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    final isIncome = transaction.type == app_models.TransactionType.income;
    final categoryIcon = _getCategoryIcon(transaction.category);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 15,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: isIncome
                      ? [Colors.green.shade400, Colors.green.shade600]
                      : [Colors.red.shade400, Colors.red.shade600],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: (isIncome ? Colors.green : Colors.red).withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Text(
                categoryIcon,
                style: const TextStyle(fontSize: 24),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    transaction.category,
                    style: const TextStyle(
                      fontWeight: FontWeight.w700,
                      fontSize: 17,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  if (transaction.description != null && transaction.description!.isNotEmpty)
                    Text(
                      transaction.description!,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  const SizedBox(height: 4),
                  Text(
                    '${account.bankName} • ${dateFormatter.format(transaction.date)}',
                    style: TextStyle(
                      color: Colors.grey.shade500,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${isIncome ? '+' : '-'}${formatter.format(transaction.amount)}',
                  style: TextStyle(
                    color: isIncome ? Colors.green.shade600 : Colors.red.shade600,
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(height: 8),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'delete') {
                      _deleteTransaction(transaction);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red, size: 20),
                          SizedBox(width: 8),
                          Text('Sil'),
                        ],
                      ),
                    ),
                  ],
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.more_vert,
                      color: Colors.grey.shade600,
                      size: 16,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'gıda':
        return '🍽️';
      case 'ulaşım':
        return '🚗';
      case 'kira':
        return '🏠';
      case 'faturalar':
        return '💡';
      case 'sağlık':
        return '🏥';
      case 'eğlence':
        return '🎬';
      case 'alışveriş':
        return '🛍️';
      case 'maaş':
        return '💰';
      case 'freelance':
        return '💻';
      case 'yatırım':
        return '📈';
      case 'hediye':
        return '🎁';
      default:
        return '📦';
    }
  }
}
