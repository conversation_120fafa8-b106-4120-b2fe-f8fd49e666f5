import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/market_data.dart';
import '../../services/enhanced_market_service.dart';

class CoinDetailScreen extends StatefulWidget {
  final MarketData coin;

  const CoinDetailScreen({super.key, required this.coin});

  @override
  State<CoinDetailScreen> createState() => _CoinDetailScreenState();
}

class _CoinDetailScreenState extends State<CoinDetailScreen> {
  final EnhancedMarketService _marketService = EnhancedMarketService();
  MarketData? _detailedCoin;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCoinDetails();
  }

  Future<void> _loadCoinDetails() async {
    try {
      final details = await _marketService.getCoinDetails(widget.coin.id);
      setState(() {
        _detailedCoin = details ?? widget.coin;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _detailedCoin = widget.coin;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final coin = _detailedCoin ?? widget.coin;
    
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(coin),
          SliverToBoxAdapter(
            child: _isLoading 
                ? const Center(
                    child: Padding(
                      padding: EdgeInsets.all(50),
                      child: CircularProgressIndicator(),
                    ),
                  )
                : _buildContent(coin),
          ),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar(MarketData coin) {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      backgroundColor: Colors.blue.shade600,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          coin.symbol.toUpperCase(),
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.blue.shade600, Colors.blue.shade800],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 40),
                  CircleAvatar(
                    radius: 30,
                    backgroundImage: coin.image.isNotEmpty 
                        ? NetworkImage(coin.image) 
                        : null,
                    backgroundColor: Colors.white.withValues(alpha: 0.2),
                    child: coin.image.isEmpty 
                        ? Text(
                            coin.symbol.substring(0, 1).toUpperCase(),
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    coin.name,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContent(MarketData coin) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPriceCard(coin),
          const SizedBox(height: 20),
          _buildStatsGrid(coin),
          const SizedBox(height: 20),
          _buildMarketDataCard(coin),
          const SizedBox(height: 20),
          _buildSupplyCard(coin),
        ],
      ),
    );
  }

  Widget _buildPriceCard(MarketData coin) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Güncel Fiyat',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            coin.formattedPrice,
            style: const TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: coin.isPriceUp 
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      coin.isPriceUp ? Icons.trending_up : Icons.trending_down,
                      color: coin.isPriceUp ? Colors.green : Colors.red,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${coin.isPriceUp ? '+' : ''}${coin.priceChangePercentage24h.toStringAsFixed(2)}%',
                      style: TextStyle(
                        color: coin.isPriceUp ? Colors.green : Colors.red,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Text(
                '${coin.isPriceUp ? '+' : ''}\$${coin.priceChange24h.toStringAsFixed(2)}',
                style: TextStyle(
                  color: coin.isPriceUp ? Colors.green : Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatsGrid(MarketData coin) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      childAspectRatio: 1.5,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      children: [
        _buildStatCard('24s Yüksek', coin.high24h != null ? '\$${coin.high24h!.toStringAsFixed(2)}' : 'N/A'),
        _buildStatCard('24s Düşük', coin.low24h != null ? '\$${coin.low24h!.toStringAsFixed(2)}' : 'N/A'),
        _buildStatCard('Piyasa Değeri', coin.formattedMarketCap),
        _buildStatCard('24s Hacim', coin.formattedVolume),
      ],
    );
  }

  Widget _buildStatCard(String title, String value) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildMarketDataCard(MarketData coin) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Piyasa Bilgileri',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          if (coin.marketCapRank != null)
            _buildInfoRow('Piyasa Sıralaması', '#${coin.marketCapRank}'),
          if (coin.ath != null)
            _buildInfoRow('Tüm Zamanların En Yükseği', '\$${coin.ath!.toStringAsFixed(2)}'),
          if (coin.athChangePercentage != null)
            _buildInfoRow('ATH\'den Değişim', '${coin.athChangePercentage!.toStringAsFixed(2)}%'),
          _buildInfoRow('Son Güncelleme', DateFormat('dd/MM/yyyy HH:mm').format(coin.lastUpdated)),
        ],
      ),
    );
  }

  Widget _buildSupplyCard(MarketData coin) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Arz Bilgileri',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          if (coin.circulatingSupply != null)
            _buildInfoRow('Dolaşımdaki Arz', NumberFormat('#,###').format(coin.circulatingSupply!)),
          if (coin.totalSupply != null)
            _buildInfoRow('Toplam Arz', NumberFormat('#,###').format(coin.totalSupply!)),
          if (coin.maxSupply != null)
            _buildInfoRow('Maksimum Arz', NumberFormat('#,###').format(coin.maxSupply!)),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }
}
