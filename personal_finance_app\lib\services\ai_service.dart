import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;
import '../models/transaction.dart' as app_models;
import '../models/account.dart';
import '../models/market_data.dart';

enum RecommendationType {
  investment,
  saving,
  spending,
  warning,
}

class SmartAIRecommendation {
  final String title;
  final String description;
  final RecommendationType type;
  final double confidence;
  final String? actionText;
  final DateTime createdAt;

  SmartAIRecommendation({
    required this.title,
    required this.description,
    required this.type,
    required this.confidence,
    this.actionText,
    required this.createdAt,
  });
}

class AIService {
  static final AIService _instance = AIService._internal();
  factory AIService() => _instance;
  AIService._internal();

  List<SmartAIRecommendation> generateRecommendations({
    required List<Account> accounts,
    required List<app_models.Transaction> transactions,
    required List<MarketData> marketData,
  }) {
    final recommendations = <SmartAIRecommendation>[];

    // Hesap analizi
    recommendations.addAll(_analyzeAccounts(accounts));

    // Harcama analizi
    recommendations.addAll(_analyzeSpending(transactions));

    // Piyasa analizi
    recommendations.addAll(_analyzeMarket(marketData, accounts));

    // Genel finansal sağlık analizi
    recommendations.addAll(_analyzeFinancialHealth(accounts, transactions));

    // Güven skoruna göre sırala
    recommendations.sort((a, b) => b.confidence.compareTo(a.confidence));

    return recommendations.take(5).toList(); // En iyi 5 tavsiye
  }

  List<SmartAIRecommendation> _analyzeAccounts(List<Account> accounts) {
    final recommendations = <SmartAIRecommendation>[];

    if (accounts.isEmpty) {
      recommendations.add(SmartAIRecommendation(
        title: 'İlk Hesabınızı Ekleyin',
        description: 'Finansal durumunuzu takip etmek için en az bir banka hesabı eklemeniz önerilir.',
        type: RecommendationType.warning,
        confidence: 0.9,
        actionText: 'Hesap Ekle',
        createdAt: DateTime.now(),
      ));
      return recommendations;
    }

    final totalBalance = accounts.fold(0.0, (sum, account) => sum + account.balance);

    if (totalBalance < 1000) {
      recommendations.add(SmartAIRecommendation(
        title: 'Acil Durum Fonu Oluşturun',
        description: 'Toplam bakiyeniz düşük görünüyor. Acil durumlar için en az 3 aylık gideriniz kadar para biriktirmeniz önerilir.',
        type: RecommendationType.saving,
        confidence: 0.85,
        actionText: 'Tasarruf Planı',
        createdAt: DateTime.now(),
      ));
    }

    // Tek hesap uyarısı
    if (accounts.length == 1) {
      recommendations.add(SmartAIRecommendation(
        title: 'Hesap Çeşitlendirme',
        description: 'Risk dağıtımı için farklı bankalarda hesap açmayı düşünebilirsiniz.',
        type: RecommendationType.investment,
        confidence: 0.6,
        actionText: 'Hesap Ekle',
        createdAt: DateTime.now(),
      ));
    }

    return recommendations;
  }

  List<SmartAIRecommendation> _analyzeSpending(List<app_models.Transaction> transactions) {
    final recommendations = <SmartAIRecommendation>[];

    if (transactions.isEmpty) {
      recommendations.add(SmartAIRecommendation(
        title: 'Harcama Takibine Başlayın',
        description: 'Finansal hedeflerinize ulaşmak için harcamalarınızı kaydetmeye başlayın.',
        type: RecommendationType.warning,
        confidence: 0.8,
        actionText: 'İşlem Ekle',
        createdAt: DateTime.now(),
      ));
      return recommendations;
    }

    // Son 30 günlük işlemler
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    final recentTransactions = transactions
        .where((t) => t.date.isAfter(thirtyDaysAgo))
        .toList();

    if (recentTransactions.isEmpty) {
      return recommendations;
    }

    // Gelir-gider analizi
    final totalIncome = recentTransactions
        .where((t) => t.type == app_models.TransactionType.income)
        .fold(0.0, (sum, t) => sum + t.amount);

    final totalExpense = recentTransactions
        .where((t) => t.type == app_models.TransactionType.expense)
        .fold(0.0, (sum, t) => sum + t.amount);

    if (totalExpense > totalIncome * 0.9) {
      recommendations.add(SmartAIRecommendation(
        title: 'Harcama Kontrolü',
        description: 'Son 30 günde harcamalarınız gelirinizin %${((totalExpense / totalIncome) * 100).toStringAsFixed(0)}\'ini oluşturuyor. Harcamalarınızı gözden geçirmeniz önerilir.',
        type: RecommendationType.warning,
        confidence: 0.9,
        actionText: 'Analiz Görüntüle',
        createdAt: DateTime.now(),
      ));
    }
    
    // Kategori analizi
    final categoryExpenses = <String, double>{};
    for (var transaction in recentTransactions.where((t) => t.type == app_models.TransactionType.expense)) {
      categoryExpenses[transaction.category] = 
          (categoryExpenses[transaction.category] ?? 0) + transaction.amount;
    }
    
    if (categoryExpenses.isNotEmpty) {
      final maxCategory = categoryExpenses.entries.reduce((a, b) => a.value > b.value ? a : b);
      final percentage = (maxCategory.value / totalExpense) * 100;
      
      if (percentage > 40) {
        recommendations.add(SmartAIRecommendation(
          title: 'Kategori Dengesizliği',
          description: '${maxCategory.key} kategorisinde harcamalarınız toplam giderin %${percentage.toStringAsFixed(0)}\'ini oluşturuyor. Bu kategoriyi gözden geçirmeniz önerilir.',
          type: RecommendationType.spending,
          confidence: 0.75,
          actionText: 'Detay Görüntüle',
          createdAt: DateTime.now(),
        ));
      }
    }
    
    return recommendations;
  }

  List<SmartAIRecommendation> _analyzeMarket(List<MarketData> marketData, List<Account> accounts) {
    final recommendations = <SmartAIRecommendation>[];
    
    if (marketData.isEmpty || accounts.isEmpty) {
      return recommendations;
    }
    
    final totalBalance = accounts.fold(0.0, (sum, account) => sum + account.balance);
    
    // Bitcoin analizi
    final bitcoin = marketData.firstWhere(
      (data) => data.symbol.toLowerCase().contains('bitcoin'),
      orElse: () => MarketData(
        symbol: '',
        name: '',
        currentPrice: 0,
        priceChange24h: 0,
        priceChangePercentage24h: 0,
        lastUpdated: DateTime.now(),
      ),
    );
    
    if (bitcoin.symbol.isNotEmpty) {
      if (bitcoin.priceChangePercentage24h < -5 && totalBalance > 5000) {
        recommendations.add(SmartAIRecommendation(
          title: 'Bitcoin Fırsat',
          description: 'Bitcoin son 24 saatte %${bitcoin.priceChangePercentage24h.abs().toStringAsFixed(1)} düştü. Uzun vadeli yatırım için fırsat olabilir.',
          type: RecommendationType.investment,
          confidence: 0.7,
          actionText: 'Piyasa Görüntüle',
          createdAt: DateTime.now(),
        ));
      } else if (bitcoin.priceChangePercentage24h > 10) {
        recommendations.add(SmartAIRecommendation(
          title: 'Piyasa Volatilitesi',
          description: 'Bitcoin son 24 saatte %${bitcoin.priceChangePercentage24h.toStringAsFixed(1)} arttı. Yüksek volatilite döneminde dikkatli olun.',
          type: RecommendationType.warning,
          confidence: 0.6,
          actionText: 'Piyasa Görüntüle',
          createdAt: DateTime.now(),
        ));
      }
    }
    
    return recommendations;
  }

  List<SmartAIRecommendation> _analyzeFinancialHealth(List<Account> accounts, List<app_models.Transaction> transactions) {
    final recommendations = <SmartAIRecommendation>[];
    
    if (accounts.isEmpty) return recommendations;
    
    final totalBalance = accounts.fold(0.0, (sum, account) => sum + account.balance);
    
    // Son 3 aylık işlemler
    final threeMonthsAgo = DateTime.now().subtract(const Duration(days: 90));
    final recentTransactions = transactions
        .where((t) => t.date.isAfter(threeMonthsAgo))
        .toList();
    
    if (recentTransactions.isNotEmpty) {
      final monthlyIncome = recentTransactions
          .where((t) => t.type == app_models.TransactionType.income)
          .fold(0.0, (sum, t) => sum + t.amount) / 3;
      
      final monthlyExpense = recentTransactions
          .where((t) => t.type == app_models.TransactionType.expense)
          .fold(0.0, (sum, t) => sum + t.amount) / 3;
      
      final savingsRate = monthlyIncome > 0 ? ((monthlyIncome - monthlyExpense) / monthlyIncome) * 100 : 0;
      
      if (savingsRate < 10) {
        recommendations.add(SmartAIRecommendation(
          title: 'Tasarruf Oranını Artırın',
          description: 'Aylık tasarruf oranınız %${savingsRate.toStringAsFixed(1)}. Finansal güvenlik için en az %20 tasarruf hedeflemeniz önerilir.',
          type: RecommendationType.saving,
          confidence: 0.8,
          actionText: 'Tasarruf Planı',
          createdAt: DateTime.now(),
        ));
      } else if (savingsRate > 30) {
        recommendations.add(SmartAIRecommendation(
          title: 'Yatırım Fırsatı',
          description: 'Harika! Aylık tasarruf oranınız %${savingsRate.toStringAsFixed(1)}. Bu parayı yatırıma yönlendirmeyi düşünebilirsiniz.',
          type: RecommendationType.investment,
          confidence: 0.85,
          actionText: 'Yatırım Seçenekleri',
          createdAt: DateTime.now(),
        ));
      }
    }
    
    // Finansal sağlık skoru
    double healthScore = 0;
    
    // Bakiye skoru (0-40 puan)
    if (totalBalance > 50000) healthScore += 40;
    else if (totalBalance > 20000) healthScore += 30;
    else if (totalBalance > 10000) healthScore += 20;
    else if (totalBalance > 5000) healthScore += 10;
    
    // Çeşitlilik skoru (0-20 puan)
    if (accounts.length >= 3) healthScore += 20;
    else if (accounts.length == 2) healthScore += 15;
    else if (accounts.length == 1) healthScore += 10;
    
    // İşlem aktivitesi skoru (0-20 puan)
    if (transactions.length > 50) healthScore += 20;
    else if (transactions.length > 20) healthScore += 15;
    else if (transactions.length > 10) healthScore += 10;
    else if (transactions.length > 0) healthScore += 5;
    
    // Düzenlilik skoru (0-20 puan)
    final lastWeek = DateTime.now().subtract(const Duration(days: 7));
    final recentActivity = transactions.where((t) => t.date.isAfter(lastWeek)).length;
    if (recentActivity > 5) healthScore += 20;
    else if (recentActivity > 2) healthScore += 15;
    else if (recentActivity > 0) healthScore += 10;
    
    recommendations.add(SmartAIRecommendation(
      title: 'Finansal Sağlık Skoru',
      description: 'Finansal sağlık skorunuz: ${healthScore.toInt()}/100. ${_getHealthAdvice(healthScore)}',
      type: RecommendationType.investment,
      confidence: 0.9,
      actionText: 'Detay Görüntüle',
      createdAt: DateTime.now(),
    ));
    
    return recommendations;
  }

  String _getHealthAdvice(double score) {
    if (score >= 80) return 'Mükemmel! Finansal durumunuz çok iyi.';
    if (score >= 60) return 'İyi durumdasınız, küçük iyileştirmeler yapabilirsiniz.';
    if (score >= 40) return 'Orta seviyede, finansal planlamanızı gözden geçirin.';
    if (score >= 20) return 'Finansal durumunuzu iyileştirmek için çalışmalısınız.';
    return 'Finansal planlamanıza odaklanmanız gerekiyor.';
  }

  // Basit öneriler metodu - eski API uyumluluğu için
  Future<List<SmartAIRecommendation>> getRecommendations() async {
    await Future.delayed(const Duration(seconds: 1)); // Simulate API delay

    return [
      SmartAIRecommendation(
        title: '🎯 Acil Durum Fonu Oluşturun',
        description: 'Aylık giderlerinizin 3-6 katı kadar bir acil durum fonu oluşturmanızı öneriyoruz. Bu, beklenmedik durumlar için finansal güvenlik sağlar.',
        type: RecommendationType.saving,
        confidence: 0.9,
        actionText: 'Tasarruf Planı Oluştur',
        createdAt: DateTime.now(),
      ),
      SmartAIRecommendation(
        title: '📊 Harcama Kategorilerini Analiz Edin',
        description: 'Harcamalarınızı kategorilere ayırarak hangi alanlarda tasarruf edebileceğinizi keşfedin. Gıda ve ulaşım genellikle optimize edilebilir.',
        type: RecommendationType.spending,
        confidence: 0.8,
        actionText: 'Analiz Başlat',
        createdAt: DateTime.now(),
      ),
      SmartAIRecommendation(
        title: '💰 Yatırım Fırsatlarını Değerlendirin',
        description: 'Acil durum fonunuz hazır olduktan sonra, düşük riskli yatırım araçlarını incelemeyi düşünün. Devlet tahvilleri başlangıç için uygun.',
        type: RecommendationType.investment,
        confidence: 0.7,
        actionText: 'Yatırım Seçenekleri',
        createdAt: DateTime.now(),
      ),
      SmartAIRecommendation(
        title: '⚠️ Düzenli Gelir Akışı Oluşturun',
        description: 'Finansal istikrar için düzenli gelir akışları oluşturmaya odaklanın. Yan gelir kaynakları araştırabilirsiniz.',
        type: RecommendationType.warning,
        confidence: 0.75,
        actionText: 'Gelir Planı',
        createdAt: DateTime.now(),
      ),
    ];
  }
}
