class AIRecommendation {
  final String id;
  final String title;
  final String description;
  final String category;
  final String priority; // 'high', 'medium', 'low'
  final bool actionable;
  final String estimatedImpact;
  final DateTime createdAt;
  final String? actionText;

  AIRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.priority,
    required this.actionable,
    required this.estimatedImpact,
    required this.createdAt,
    this.actionText,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'category': category,
      'priority': priority,
      'actionable': actionable ? 1 : 0,
      'estimated_impact': estimatedImpact,
      'created_at': createdAt.millisecondsSinceEpoch,
      'action_text': actionText,
    };
  }

  factory AIRecommendation.fromMap(Map<String, dynamic> map) {
    return AIRecommendation(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      category: map['category'] ?? '',
      priority: map['priority'] ?? 'medium',
      actionable: (map['actionable'] ?? 0) == 1,
      estimatedImpact: map['estimated_impact'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] ?? 0),
      actionText: map['action_text'],
    );
  }

  AIRecommendation copyWith({
    String? id,
    String? title,
    String? description,
    String? category,
    String? priority,
    bool? actionable,
    String? estimatedImpact,
    DateTime? createdAt,
    String? actionText,
  }) {
    return AIRecommendation(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
      priority: priority ?? this.priority,
      actionable: actionable ?? this.actionable,
      estimatedImpact: estimatedImpact ?? this.estimatedImpact,
      createdAt: createdAt ?? this.createdAt,
      actionText: actionText ?? this.actionText,
    );
  }

  @override
  String toString() {
    return 'AIRecommendation(id: $id, title: $title, category: $category, priority: $priority)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AIRecommendation && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
