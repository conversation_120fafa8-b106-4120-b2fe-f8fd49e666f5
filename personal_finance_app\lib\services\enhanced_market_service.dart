import 'dart:convert';
import 'dart:async';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/market_data.dart';

class EnhancedMarketService {
  static final EnhancedMarketService _instance = EnhancedMarketService._internal();
  factory EnhancedMarketService() => _instance;
  EnhancedMarketService._internal();

  static const String _baseUrl = 'https://api.coingecko.com/api/v3';
  static const String _cacheKey = 'market_data_cache';
  static const Duration _cacheTimeout = Duration(minutes: 5);
  
  List<MarketData> _cachedData = [];
  DateTime? _lastFetch;
  Timer? _refreshTimer;
  
  final StreamController<List<MarketData>> _marketDataController = 
      StreamController<List<MarketData>>.broadcast();
  
  Stream<List<MarketData>> get marketDataStream => _marketDataController.stream;
  
  List<MarketData> get cachedMarketData => _cachedData;

  Future<void> initialize() async {
    await _loadCachedData();
    await fetchMarketData();
    _startAutoRefresh();
  }

  void _startAutoRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(const Duration(minutes: 2), (timer) {
      fetchMarketData();
    });
  }

  Future<List<MarketData>> fetchMarketData({
    int page = 1,
    int perPage = 100,
    String order = 'market_cap_desc',
  }) async {
    try {
      // Cache kontrolü
      if (_lastFetch != null && 
          DateTime.now().difference(_lastFetch!) < _cacheTimeout &&
          _cachedData.isNotEmpty) {
        return _cachedData;
      }

      final url = '$_baseUrl/coins/markets?vs_currency=usd&order=$order&per_page=$perPage&page=$page&sparkline=false&price_change_percentage=24h';
      
      final response = await http.get(
        Uri.parse(url),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        
        final marketData = jsonData.map((json) => MarketData.fromJson(json)).toList();
        
        _cachedData = marketData;
        _lastFetch = DateTime.now();
        
        await _saveCachedData();
        _marketDataController.add(marketData);
        
        return marketData;
      } else {
        throw Exception('API Error: ${response.statusCode}');
      }
    } catch (e) {
      // Hata durumunda cache'deki veriyi döndür
      if (_cachedData.isNotEmpty) {
        return _cachedData;
      }
      throw Exception('Market data fetch failed: $e');
    }
  }

  Future<List<MarketData>> searchCoins(String query) async {
    if (query.isEmpty) return _cachedData;
    
    try {
      final url = '$_baseUrl/search?query=${Uri.encodeComponent(query)}';
      
      final response = await http.get(
        Uri.parse(url),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final coins = data['coins'] as List<dynamic>;
        
        // Arama sonuçlarını mevcut market data ile eşleştir
        final searchResults = <MarketData>[];
        
        for (final coin in coins.take(20)) {
          final coinId = coin['id'];
          final existingData = _cachedData.firstWhere(
            (data) => data.id == coinId,
            orElse: () => MarketData(
              id: coinId,
              symbol: coin['symbol'] ?? '',
              name: coin['name'] ?? '',
              currentPrice: 0,
              priceChange24h: 0,
              priceChangePercentage24h: 0,
              marketCap: 0,
              volume24h: 0,
              image: coin['large'] ?? '',
              lastUpdated: DateTime.now(),
            ),
          );
          searchResults.add(existingData);
        }
        
        return searchResults;
      }
    } catch (e) {
      // Hata durumunda local arama yap
      return _cachedData.where((data) =>
        data.name.toLowerCase().contains(query.toLowerCase()) ||
        data.symbol.toLowerCase().contains(query.toLowerCase())
      ).toList();
    }
    
    return [];
  }

  Future<MarketData?> getCoinDetails(String coinId) async {
    try {
      final url = '$_baseUrl/coins/$coinId?localization=false&tickers=false&market_data=true&community_data=false&developer_data=false&sparkline=false';
      
      final response = await http.get(
        Uri.parse(url),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final marketData = data['market_data'];
        
        return MarketData(
          id: data['id'],
          symbol: data['symbol'] ?? '',
          name: data['name'] ?? '',
          currentPrice: (marketData['current_price']?['usd'] ?? 0).toDouble(),
          priceChange24h: (marketData['price_change_24h'] ?? 0).toDouble(),
          priceChangePercentage24h: (marketData['price_change_percentage_24h'] ?? 0).toDouble(),
          marketCap: (marketData['market_cap']?['usd'] ?? 0).toDouble(),
          volume24h: (marketData['total_volume']?['usd'] ?? 0).toDouble(),
          image: data['image']?['large'] ?? '',
          lastUpdated: DateTime.now(),
          marketCapRank: marketData['market_cap_rank']?.toInt(),
          high24h: (marketData['high_24h']?['usd'] ?? 0).toDouble(),
          low24h: (marketData['low_24h']?['usd'] ?? 0).toDouble(),
          ath: (marketData['ath']?['usd'] ?? 0).toDouble(),
          athChangePercentage: (marketData['ath_change_percentage']?['usd'] ?? 0).toDouble(),
          athDate: marketData['ath_date']?['usd'] != null 
              ? DateTime.tryParse(marketData['ath_date']['usd']) 
              : null,
          circulatingSupply: (marketData['circulating_supply'] ?? 0).toDouble(),
          totalSupply: (marketData['total_supply'] ?? 0).toDouble(),
          maxSupply: (marketData['max_supply'] ?? 0).toDouble(),
        );
      }
    } catch (e) {
      // Hata durumunda cache'den bul
      try {
        return _cachedData.firstWhere((data) => data.id == coinId);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  Future<List<MarketData>> getTrendingCoins() async {
    try {
      final url = '$_baseUrl/search/trending';
      
      final response = await http.get(
        Uri.parse(url),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final trending = data['coins'] as List<dynamic>;
        
        final trendingData = <MarketData>[];
        
        for (final coin in trending) {
          final coinData = coin['item'];
          final existingData = _cachedData.firstWhere(
            (data) => data.id == coinData['id'],
            orElse: () => MarketData(
              id: coinData['id'],
              symbol: coinData['symbol'] ?? '',
              name: coinData['name'] ?? '',
              currentPrice: 0,
              priceChange24h: 0,
              priceChangePercentage24h: 0,
              marketCap: 0,
              volume24h: 0,
              image: coinData['large'] ?? '',
              lastUpdated: DateTime.now(),
              marketCapRank: coinData['market_cap_rank']?.toInt(),
            ),
          );
          trendingData.add(existingData);
        }
        
        return trendingData;
      }
    } catch (e) {
      // Hata durumunda top 10 döndür
      return _cachedData.take(10).toList();
    }
    
    return [];
  }

  Future<void> _loadCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_cacheKey);
      final lastFetchStr = prefs.getString('${_cacheKey}_timestamp');
      
      if (cachedJson != null && lastFetchStr != null) {
        final lastFetch = DateTime.tryParse(lastFetchStr);
        if (lastFetch != null && 
            DateTime.now().difference(lastFetch) < const Duration(hours: 1)) {
          final List<dynamic> jsonData = json.decode(cachedJson);
          _cachedData = jsonData.map((json) => MarketData.fromJson(json)).toList();
          _lastFetch = lastFetch;
        }
      }
    } catch (e) {
      // Cache yükleme hatası - sessizce devam et
    }
  }

  Future<void> _saveCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonData = _cachedData.map((data) => data.toJson()).toList();
      await prefs.setString(_cacheKey, json.encode(jsonData));
      await prefs.setString('${_cacheKey}_timestamp', DateTime.now().toIso8601String());
    } catch (e) {
      // Cache kaydetme hatası - sessizce devam et
    }
  }

  void dispose() {
    _refreshTimer?.cancel();
    _marketDataController.close();
  }
}
