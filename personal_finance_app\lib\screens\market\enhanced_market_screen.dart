import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../services/enhanced_market_service.dart';
import '../../models/market_data.dart';
import 'coin_detail_screen.dart';

class EnhancedMarketScreen extends StatefulWidget {
  const EnhancedMarketScreen({super.key});

  @override
  State<EnhancedMarketScreen> createState() => _EnhancedMarketScreenState();
}

class _EnhancedMarketScreenState extends State<EnhancedMarketScreen>
    with TickerProviderStateMixin {
  final EnhancedMarketService _marketService = EnhancedMarketService();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  List<MarketData> _marketData = [];
  List<MarketData> _filteredData = [];
  List<MarketData> _trendingCoins = [];
  bool _isLoading = true;
  bool _isSearching = false;
  bool _isConnected = true;
  String _sortBy = 'market_cap_desc';
  DateTime? _lastUpdate;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    
    _initializeData();
    _setupScrollListener();
    _setupSearchListener();
  }

  void _initializeData() async {
    // Connection kontrolü
    _isConnected = await _marketService.checkConnection();

    await _marketService.initialize();
    _loadMarketData();
    _loadTrendingCoins();

    // Market data stream'ini dinle
    _marketService.marketDataStream.listen((data) {
      if (mounted) {
        setState(() {
          _marketData = data;
          _filteredData = _isSearching ? _filteredData : data;
          _isLoading = false;
          _lastUpdate = DateTime.now();
        });
        _animationController.forward();
      }
    });

    // Loading stream'ini dinle
    _marketService.loadingStream.listen((isLoading) {
      if (mounted) {
        setState(() {
          _isLoading = isLoading;
        });
      }
    });
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        // Load more data when reaching bottom
        _loadMoreData();
      }
    });
  }

  void _setupSearchListener() {
    _searchController.addListener(() {
      _performSearch(_searchController.text);
    });
  }

  Future<void> _loadMarketData() async {
    try {
      final data = await _marketService.fetchMarketData(perPage: 100);
      setState(() {
        _marketData = data;
        _filteredData = data;
        _isLoading = false;
      });
      _animationController.forward();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Market verileri yüklenemedi');
    }
  }

  Future<void> _loadTrendingCoins() async {
    try {
      final trending = await _marketService.getTrendingCoins();
      setState(() {
        _trendingCoins = trending;
      });
    } catch (e) {
      // Trending coins yüklenemedi - sessizce devam et
    }
  }

  Future<void> _loadMoreData() async {
    // Implement pagination if needed
  }

  void _performSearch(String query) async {
    setState(() {
      _isSearching = query.isNotEmpty;
    });

    if (query.isEmpty) {
      setState(() {
        _filteredData = _marketData;
      });
      return;
    }

    try {
      final results = await _marketService.searchCoins(query);
      setState(() {
        _filteredData = results;
      });
    } catch (e) {
      // Local search as fallback
      setState(() {
        _filteredData = _marketData.where((coin) =>
          coin.name.toLowerCase().contains(query.toLowerCase()) ||
          coin.symbol.toLowerCase().contains(query.toLowerCase())
        ).toList();
      });
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            if (_trendingCoins.isNotEmpty) _buildTrendingSection(),
            _buildSearchBar(),
            _buildSortOptions(),
            Expanded(child: _buildMarketList()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.blue.shade600, Colors.blue.shade800],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Kripto Piyasası',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Row(
                children: [
                  // Connection status indicator
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: _isConnected ? Colors.green : Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: _isLoading ? null : () async {
                      await _marketService.forceRefresh();
                      await _loadTrendingCoins();
                    },
                    icon: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Icon(Icons.refresh, color: Colors.white),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                'Son güncelleme: ${_lastUpdate != null ? DateFormat('HH:mm:ss').format(_lastUpdate!) : 'Yükleniyor...'}',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 14,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '• ${_marketData.length} coin',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTrendingSection() {
    return Container(
      height: 120,
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              'Trend Coinler',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _trendingCoins.length,
              itemBuilder: (context, index) {
                final coin = _trendingCoins[index];
                return _buildTrendingCard(coin);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrendingCard(MarketData coin) {
    return Container(
      width: 140,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 10,
                backgroundImage: coin.image.isNotEmpty
                    ? NetworkImage(coin.image)
                    : null,
                backgroundColor: Colors.grey[300],
                child: coin.image.isEmpty
                    ? Text(
                        coin.symbol.substring(0, 1).toUpperCase(),
                        style: const TextStyle(fontSize: 10),
                      )
                    : null,
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  coin.symbol.toUpperCase(),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 11,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            coin.formattedPrice,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 13,
            ),
            overflow: TextOverflow.ellipsis,
          ),
          Text(
            '${coin.priceChangePercentage24h.toStringAsFixed(1)}%',
            style: TextStyle(
              color: coin.isPriceUp ? Colors.green : Colors.red,
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: const InputDecoration(
          hintText: 'Coin ara...',
          prefixIcon: Icon(Icons.search, color: Colors.grey),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),
    );
  }

  Widget _buildSortOptions() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildSortChip('Piyasa Değeri', 'market_cap_desc'),
          _buildSortChip('Fiyat', 'price_desc'),
          _buildSortChip('24s Değişim', 'price_change_percentage_24h_desc'),
          _buildSortChip('Hacim', 'volume_desc'),
          _buildSortChip('İsim', 'name_asc'),
        ],
      ),
    );
  }

  Widget _buildSortChip(String label, String value) {
    final isSelected = _sortBy == value;
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _sortBy = value;
          });
          _sortData();
        },
        selectedColor: Colors.blue.withValues(alpha: 0.2),
        checkmarkColor: Colors.blue,
      ),
    );
  }

  void _sortData() {
    setState(() {
      switch (_sortBy) {
        case 'market_cap_desc':
          _filteredData.sort((a, b) => b.marketCap.compareTo(a.marketCap));
          break;
        case 'price_desc':
          _filteredData.sort((a, b) => b.currentPrice.compareTo(a.currentPrice));
          break;
        case 'price_change_percentage_24h_desc':
          _filteredData.sort((a, b) => b.priceChangePercentage24h.compareTo(a.priceChangePercentage24h));
          break;
        case 'volume_desc':
          _filteredData.sort((a, b) => b.volume24h.compareTo(a.volume24h));
          break;
        case 'name_asc':
          _filteredData.sort((a, b) => a.name.compareTo(b.name));
          break;
      }
    });
  }

  Widget _buildMarketList() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Market verileri yükleniyor...'),
          ],
        ),
      );
    }

    if (_filteredData.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _isSearching ? 'Arama sonucu bulunamadı' : 'Market verisi bulunamadı',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: RefreshIndicator(
        onRefresh: () async {
          await _marketService.forceRefresh();
          await _loadTrendingCoins();
        },
        child: ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          itemCount: _filteredData.length + 1, // +1 for loading indicator
          itemBuilder: (context, index) {
            if (index == _filteredData.length) {
              // Loading indicator at bottom
              return _isLoading
                  ? const Padding(
                      padding: EdgeInsets.all(20),
                      child: Center(child: CircularProgressIndicator()),
                    )
                  : const SizedBox.shrink();
            }

            final coin = _filteredData[index];
            return _buildCoinCard(coin, index);
          },
        ),
      ),
    );
  }

  Widget _buildCoinCard(MarketData coin, int index) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: coin.isPriceUp
            ? Border.all(color: Colors.green.withValues(alpha: 0.3), width: 1)
            : coin.priceChangePercentage24h < 0
                ? Border.all(color: Colors.red.withValues(alpha: 0.3), width: 1)
                : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _navigateToCoinDetail(coin),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Rank
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      '${coin.marketCapRank ?? index + 1}',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),

                // Coin Image & Info
                Expanded(
                  flex: 3,
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundImage: coin.image.isNotEmpty
                            ? NetworkImage(coin.image)
                            : null,
                        backgroundColor: Colors.grey[300],
                        child: coin.image.isEmpty
                            ? Text(
                                coin.symbol.substring(0, 1).toUpperCase(),
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              )
                            : null,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              coin.name,
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            Row(
                              children: [
                                Text(
                                  coin.symbol.toUpperCase(),
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 14,
                                  ),
                                ),
                                if (coin.marketCap > 0) ...[
                                  const SizedBox(width: 8),
                                  Text(
                                    coin.formattedMarketCap,
                                    style: TextStyle(
                                      color: Colors.grey[500],
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Price & Change
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      AnimatedDefaultTextStyle(
                        duration: const Duration(milliseconds: 300),
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          color: coin.isPriceUp
                              ? Colors.green.shade700
                              : coin.priceChangePercentage24h < 0
                                  ? Colors.red.shade700
                                  : Colors.black87,
                        ),
                        child: Text(coin.formattedPrice),
                      ),
                      const SizedBox(height: 4),
                      AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: coin.isPriceUp
                              ? Colors.green.withValues(alpha: 0.15)
                              : Colors.red.withValues(alpha: 0.15),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              coin.isPriceUp ? Icons.trending_up : Icons.trending_down,
                              size: 12,
                              color: coin.isPriceUp ? Colors.green : Colors.red,
                            ),
                            const SizedBox(width: 2),
                            Text(
                              '${coin.isPriceUp ? '+' : ''}${coin.priceChangePercentage24h.toStringAsFixed(2)}%',
                              style: TextStyle(
                                color: coin.isPriceUp ? Colors.green : Colors.red,
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToCoinDetail(MarketData coin) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CoinDetailScreen(coin: coin),
      ),
    );
  }
}
