import 'package:flutter/material.dart';
import 'package:email_validator/email_validator.dart';
import '../../services/auth_service.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/pin_input_field.dart';
import '../main/main_screen.dart';

class SimpleSetupScreen extends StatefulWidget {
  const SimpleSetupScreen({super.key});

  @override
  State<SimpleSetupScreen> createState() => _SimpleSetupScreenState();
}

class _SimpleSetupScreenState extends State<SimpleSetupScreen> {
  final PageController _pageController = PageController();
  final AuthService _authService = AuthService();
  
  // Form controllers
  final _emailController = TextEditingController();
  final _pinController = TextEditingController();
  final _confirmPinController = TextEditingController();
  
  int _currentPage = 0;
  bool _isLoading = false;
  String? _errorMessage;
  bool _skipEmail = false;

  @override
  void dispose() {
    _pageController.dispose();
    _emailController.dispose();
    _pinController.dispose();
    _confirmPinController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _completeSetup() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final email = _skipEmail ? '<EMAIL>' : _emailController.text.trim();
      
      final success = await _authService.register(
        email,
        _pinController.text,
      );

      if (success) {
        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const MainScreen()),
          );
        }
      } else {
        setState(() {
          _errorMessage = 'Kurulum sırasında bir hata oluştu';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Kurulum sırasında bir hata oluştu';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: _currentPage > 0
            ? IconButton(
                icon: const Icon(Icons.arrow_back_ios, color: Colors.black87),
                onPressed: _previousPage,
              )
            : IconButton(
                icon: const Icon(Icons.close, color: Colors.black87),
                onPressed: () => Navigator.of(context).pop(),
              ),
        title: Text(
          _skipEmail ? 'PIN Oluştur' : 'Kurulum',
          style: const TextStyle(
            color: Colors.black87,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentPage = index;
          });
        },
        children: [
          _buildEmailOrSkipPage(),
          _buildPinPage(),
        ],
      ),
    );
  }

  Widget _buildEmailOrSkipPage() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 32),
          
          const Text(
            'Güvenlik Kurulumu',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Text(
            'Uygulamayı kullanmaya başlamak için 4 haneli bir PIN oluşturacaksınız.',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
              height: 1.5,
            ),
          ),
          
          const SizedBox(height: 32),
          
          // E-posta seçeneği
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.email_outlined,
                      color: Colors.blue.shade700,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'E-posta Adresi (Opsiyonel)',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  'PIN\'inizi unutursanız sıfırlama bağlantısı göndermek için kullanılır.',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                  ),
                ),
                const SizedBox(height: 16),
                CustomTextField(
                  controller: _emailController,
                  labelText: 'E-posta Adresi',
                  hintText: '<EMAIL> (opsiyonel)',
                  keyboardType: TextInputType.emailAddress,
                  prefixIcon: Icons.email_outlined,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Atlama seçeneği
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.security,
                      color: Colors.grey.shade700,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'Sadece PIN ile Devam Et',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'E-posta olmadan da kullanabilirsiniz. PIN\'i unutursanız uygulamayı sıfırlamanız gerekir.',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          
          const Spacer(),
          
          // Devam butonu
          CustomButton(
            text: 'PIN Oluştur',
            onPressed: () {
              if (_emailController.text.trim().isNotEmpty) {
                if (!EmailValidator.validate(_emailController.text.trim())) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Geçerli bir e-posta adresi girin veya boş bırakın')),
                  );
                  return;
                }
                _skipEmail = false;
              } else {
                _skipEmail = true;
              }
              _nextPage();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPinPage() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 32),
          
          const Text(
            'PIN Oluştur',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Text(
            'Uygulamaya her girişte bu PIN\'i kullanacaksınız.',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
              height: 1.5,
            ),
          ),
          
          const SizedBox(height: 48),
          
          PinInputField(
            controller: _pinController,
            labelText: 'PIN (4 haneli)',
          ),
          
          const SizedBox(height: 24),
          
          PinInputField(
            controller: _confirmPinController,
            labelText: 'PIN Onayı',
          ),
          
          const SizedBox(height: 24),
          
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.amber.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.amber.shade200),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.warning_amber_outlined,
                  color: Colors.amber.shade700,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'PIN\'inizi unutmayın! ${_skipEmail ? "E-posta girmediniz, PIN\'i unutursanız tüm verileriniz silinir." : "Unutursanız e-posta ile sıfırlayabilirsiniz."}',
                    style: TextStyle(
                      color: Colors.amber.shade700,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          if (_errorMessage != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Text(
                _errorMessage!,
                style: TextStyle(
                  color: Colors.red.shade700,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
          
          const Spacer(),
          
          CustomButton(
            text: 'Kurulumu Tamamla',
            onPressed: _isLoading ? null : () {
              if (_pinController.text.length != 4) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('PIN 4 haneli olmalı')),
                );
                return;
              }
              
              if (!RegExp(r'^\d+$').hasMatch(_pinController.text)) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('PIN sadece rakamlardan oluşmalı')),
                );
                return;
              }
              
              if (_confirmPinController.text != _pinController.text) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('PIN\'ler eşleşmiyor')),
                );
                return;
              }
              
              _completeSetup();
            },
            isLoading: _isLoading,
          ),
        ],
      ),
    );
  }
}
