import 'package:flutter/material.dart';

class SimpleAIScreen extends StatefulWidget {
  const SimpleAIScreen({super.key});

  @override
  State<SimpleAIScreen> createState() => _SimpleAIScreenState();
}

class _SimpleAIScreenState extends State<SimpleAIScreen> {
  bool _isLoading = false;
  List<Map<String, dynamic>> _recommendations = [];

  @override
  void initState() {
    super.initState();
    _loadRecommendations();
  }

  Future<void> _loadRecommendations() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate loading
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _recommendations = [
        {
          'title': '🎯 Acil Durum Fonu Oluşturun',
          'description': 'Aylık giderlerinizin 3-6 katı kadar bir acil durum fonu oluşturmanızı öneriyoruz. Bu, beklenmedik durumlar için finansal güvenlik sağlar.',
          'category': 'Tasarruf',
          'priority': '<PERSON><PERSON><PERSON><PERSON>',
          'color': Colors.green,
          'icon': Icons.savings,
        },
        {
          'title': '📊 Harcama Kategorilerini Analiz Edin',
          'description': 'Harcamalarınızı kategorilere ayırarak hangi alanlarda tasarruf edebileceğinizi keşfedin. Gıda ve ulaşım genellikle optimize edilebilir.',
          'category': 'Analiz',
          'priority': 'Orta',
          'color': Colors.orange,
          'icon': Icons.analytics,
        },
        {
          'title': '💰 Yatırım Fırsatlarını Değerlendirin',
          'description': 'Acil durum fonunuz hazır olduktan sonra, düşük riskli yatırım araçlarını incelemeyi düşünün. Devlet tahvilleri başlangıç için uygun.',
          'category': 'Yatırım',
          'priority': 'Düşük',
          'color': Colors.blue,
          'icon': Icons.trending_up,
        },
        {
          'title': '⚠️ Düzenli Gelir Akışı Oluşturun',
          'description': 'Finansal istikrar için düzenli gelir akışları oluşturmaya odaklanın. Yan gelir kaynakları araştırabilirsiniz.',
          'category': 'Gelir',
          'priority': 'Orta',
          'color': Colors.purple,
          'icon': Icons.account_balance_wallet,
        },
        {
          'title': '📱 Finansal Uygulamaları Kullanın',
          'description': 'Harcama takibi ve bütçe yönetimi için finansal uygulamaları aktif olarak kullanın. Bu uygulama da bunlardan biri!',
          'category': 'Teknoloji',
          'priority': 'Düşük',
          'color': Colors.teal,
          'icon': Icons.phone_android,
        },
      ];
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'AI Önerileri',
          style: TextStyle(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadRecommendations,
          ),
        ],
      ),
      body: _isLoading ? _buildLoadingView() : _buildRecommendationsView(),
    );
  }

  Widget _buildLoadingView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'AI önerileri hazırlanıyor...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationsView() {
    return RefreshIndicator(
      onRefresh: _loadRecommendations,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildHeader(),
          const SizedBox(height: 20),
          ..._recommendations.map((recommendation) => 
            _buildRecommendationCard(recommendation)
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade400, Colors.purple.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.psychology,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'AI Finansal Danışmanınız',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Kişiselleştirilmiş öneriler',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '${_recommendations.length} öneri mevcut',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationCard(Map<String, dynamic> recommendation) {
    final color = recommendation['color'] as Color;
    final icon = recommendation['icon'] as IconData;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        recommendation['title'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: color.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              recommendation['category'],
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                                color: color,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: _getPriorityColor(recommendation['priority']).withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              recommendation['priority'],
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                                color: _getPriorityColor(recommendation['priority']),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  recommendation['description'],
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton.icon(
                      onPressed: () {
                        _showRecommendationDetails(recommendation);
                      },
                      icon: Icon(
                        Icons.arrow_forward,
                        size: 16,
                        color: color,
                      ),
                      label: Text(
                        'Detaylar',
                        style: TextStyle(
                          color: color,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority) {
      case 'Yüksek':
        return Colors.red;
      case 'Orta':
        return Colors.orange;
      case 'Düşük':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  void _showRecommendationDetails(Map<String, dynamic> recommendation) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      recommendation['title'],
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      recommendation['description'],
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade600,
                        height: 1.5,
                      ),
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'Önerilen Adımlar:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildActionSteps(recommendation['category']),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionSteps(String category) {
    List<String> steps = [];
    
    switch (category) {
      case 'Tasarruf':
        steps = [
          'Aylık giderlerinizi hesaplayın',
          'Hedef tutarı belirleyin (3-6 aylık gider)',
          'Otomatik transfer kurun',
          'Ayrı bir hesapta saklayın',
        ];
        break;
      case 'Analiz':
        steps = [
          'Son 3 ayın harcamalarını gözden geçirin',
          'Kategorilere göre gruplandırın',
          'En yüksek harcama alanlarını belirleyin',
          'Tasarruf hedefleri koyun',
        ];
        break;
      case 'Yatırım':
        steps = [
          'Risk toleransınızı belirleyin',
          'Yatırım hedeflerinizi tanımlayın',
          'Araştırma yapın',
          'Küçük miktarlarla başlayın',
        ];
        break;
      default:
        steps = [
          'Mevcut durumunuzu analiz edin',
          'Hedeflerinizi belirleyin',
          'Plan oluşturun',
          'Adım adım uygulayın',
        ];
    }

    return Column(
      children: steps.asMap().entries.map((entry) {
        int index = entry.key;
        String step = entry.value;
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: Colors.blue.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.blue.shade700,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  step,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.3,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
