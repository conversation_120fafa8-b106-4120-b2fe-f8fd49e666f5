import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user.dart';
import 'database_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  
  static const String _currentUserIdKey = 'current_user_id';
  static const String _isLoggedInKey = 'is_logged_in';

  User? _currentUser;
  User? get currentUser => _currentUser;

  bool get isLoggedIn => _currentUser != null;

  // PIN'i hash'le
  String _hashPin(String pin) {
    var bytes = utf8.encode(pin);
    var digest = sha256.convert(bytes);
    return digest.toString();
  }

  // <PERSON><PERSON>ı<PERSON>ı kaydı
  Future<bool> register(String email, String pin) async {
    try {
      // E-posta zaten kayıtlı mı kontrol et
      final existingUser = await _databaseService.getUserByEmail(email);
      if (existingUser != null) {
        return false; // E-posta zaten kayıtlı
      }

      // Yeni kullanıcı oluştur
      final hashedPin = _hashPin(pin);
      final user = User(
        email: email,
        pinHash: hashedPin,
        createdAt: DateTime.now(),
      );

      final userId = await _databaseService.insertUser(user);
      
      // Kullanıcıyı oturum açmış olarak işaretle
      _currentUser = user.copyWith(id: userId);
      await _saveLoginState();
      
      return true;
    } catch (e) {
      print('Registration error: $e');
      return false;
    }
  }

  // Giriş yapma
  Future<bool> login(String email, String pin) async {
    try {
      final user = await _databaseService.getUserByEmail(email);
      if (user == null) {
        return false; // Kullanıcı bulunamadı
      }

      final hashedPin = _hashPin(pin);
      if (user.pinHash != hashedPin) {
        return false; // Yanlış PIN
      }

      // Son giriş zamanını güncelle
      await _databaseService.updateUserLastLogin(user.id!);
      
      _currentUser = user.copyWith(lastLoginAt: DateTime.now());
      await _saveLoginState();
      
      return true;
    } catch (e) {
      print('Login error: $e');
      return false;
    }
  }

  // Çıkış yapma
  Future<void> logout() async {
    _currentUser = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_currentUserIdKey);
    await prefs.setBool(_isLoggedInKey, false);
  }

  // Oturum durumunu kaydet
  Future<void> _saveLoginState() async {
    if (_currentUser != null) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_currentUserIdKey, _currentUser!.id!);
      await prefs.setBool(_isLoggedInKey, true);
    }
  }

  // Uygulama başlatıldığında oturum durumunu kontrol et
  Future<bool> checkLoginStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isLoggedIn = prefs.getBool(_isLoggedInKey) ?? false;
      
      if (!isLoggedIn) {
        return false;
      }

      final userId = prefs.getInt(_currentUserIdKey);
      if (userId == null) {
        return false;
      }

      // Kullanıcı bilgilerini veritabanından al
      final users = await _databaseService.database;
      final userMaps = await users.query(
        'users',
        where: 'id = ?',
        whereArgs: [userId],
      );

      if (userMaps.isEmpty) {
        await logout(); // Kullanıcı bulunamadı, çıkış yap
        return false;
      }

      _currentUser = User.fromMap(userMaps.first);
      return true;
    } catch (e) {
      print('Check login status error: $e');
      return false;
    }
  }

  // PIN değiştirme
  Future<bool> changePin(String currentPin, String newPin) async {
    if (_currentUser == null) return false;

    try {
      final hashedCurrentPin = _hashPin(currentPin);
      if (_currentUser!.pinHash != hashedCurrentPin) {
        return false; // Mevcut PIN yanlış
      }

      final hashedNewPin = _hashPin(newPin);
      final updatedUser = _currentUser!.copyWith(pinHash: hashedNewPin);
      
      final db = await _databaseService.database;
      await db.update(
        'users',
        {'pin_hash': hashedNewPin},
        where: 'id = ?',
        whereArgs: [_currentUser!.id],
      );

      _currentUser = updatedUser;
      return true;
    } catch (e) {
      print('Change PIN error: $e');
      return false;
    }
  }

  // PIN sıfırlama (e-posta ile)
  Future<bool> resetPin(String email) async {
    try {
      final user = await _databaseService.getUserByEmail(email);
      if (user == null) {
        return false; // Kullanıcı bulunamadı
      }

      // Gerçek uygulamada burada e-posta gönderme işlemi olacak
      // Şimdilik sadece true döndürüyoruz
      print('Password reset email would be sent to: $email');
      return true;
    } catch (e) {
      print('Reset PIN error: $e');
      return false;
    }
  }

  // Güvenli depolama için yardımcı metodlar
  Future<void> storeSecureData(String key, String value) async {
    await _secureStorage.write(key: key, value: value);
  }

  Future<String?> getSecureData(String key) async {
    return await _secureStorage.read(key: key);
  }

  Future<void> deleteSecureData(String key) async {
    await _secureStorage.delete(key: key);
  }

  Future<void> clearAllSecureData() async {
    await _secureStorage.deleteAll();
  }
}
