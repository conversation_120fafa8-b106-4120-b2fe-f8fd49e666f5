import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/market_data.dart';

class MarketService {
  static final MarketService _instance = MarketService._internal();
  factory MarketService() => _instance;
  MarketService._internal();

  static const String _baseUrl = 'https://api.coingecko.com/api/v3';
  static const String _cacheKey = 'market_data_cache';
  static const String _lastUpdateKey = 'market_data_last_update';
  static const Duration _cacheExpiry = Duration(minutes: 5);

  List<MarketData> _cachedData = [];
  DateTime? _lastUpdate;

  // Favori varlıklar (varsayılan olarak Bitcoin ve Ethereum)
  final List<String> _defaultAssets = ['bitcoin', 'ethereum'];
  List<String> _favoriteAssets = [];

  List<MarketData> get cachedData => _cachedData;
  List<String> get favoriteAssets => _favoriteAssets;

  Future<void> initialize() async {
    await _loadFavoriteAssets();
    await _loadCachedData();
  }

  // Piyasa verilerini çek
  Future<List<MarketData>> fetchMarketData({bool forceRefresh = false}) async {
    try {
      // Cache kontrolü
      if (!forceRefresh && _isCacheValid()) {
        return _cachedData;
      }

      final assets = _favoriteAssets.isEmpty ? _defaultAssets : _favoriteAssets;
      final assetIds = assets.join(',');
      
      final url = '$_baseUrl/simple/price?ids=$assetIds&vs_currencies=usd,try&include_24hr_change=true&include_last_updated_at=true';
      
      final response = await http.get(
        Uri.parse(url),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        
        _cachedData = _parseMarketData(data);
        _lastUpdate = DateTime.now();
        
        await _saveCachedData();
        return _cachedData;
      } else {
        throw Exception('Failed to fetch market data: ${response.statusCode}');
      }
    } catch (e) {
      // Market data fetch error - silently handle
      // Hata durumunda cache'deki veriyi döndür
      return _cachedData;
    }
  }

  // Detaylı coin bilgisi çek
  Future<Map<String, dynamic>?> fetchCoinDetails(String coinId) async {
    try {
      final url = '$_baseUrl/coins/$coinId?localization=false&tickers=false&market_data=true&community_data=false&developer_data=false&sparkline=false';
      
      final response = await http.get(
        Uri.parse(url),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      // Coin details fetch error - silently handle
      return null;
    }
  }

  // Trending coins çek
  Future<List<Map<String, dynamic>>> fetchTrendingCoins() async {
    try {
      final url = '$_baseUrl/search/trending';
      
      final response = await http.get(
        Uri.parse(url),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        final coins = data['coins'] as List<dynamic>;
        return coins.map((coin) => coin['item'] as Map<String, dynamic>).toList();
      }
      return [];
    } catch (e) {
      // Trending coins fetch error - silently handle
      return [];
    }
  }

  // Piyasa verilerini parse et
  List<MarketData> _parseMarketData(Map<String, dynamic> data) {
    List<MarketData> marketDataList = [];
    
    data.forEach((coinId, coinData) {
      if (coinData is Map<String, dynamic>) {
        try {
          final marketData = MarketData(
            id: coinId,
            symbol: coinId.toUpperCase(),
            name: _getCoinName(coinId),
            currentPrice: (coinData['usd'] ?? 0).toDouble(),
            priceChange24h: 0.0, // CoinGecko simple API'sinde bu bilgi yok
            priceChangePercentage24h: (coinData['usd_24h_change'] ?? 0).toDouble(),
            marketCap: 0.0,
            volume24h: 0.0,
            image: '',
            lastUpdated: DateTime.fromMillisecondsSinceEpoch(
              (coinData['last_updated_at'] ?? DateTime.now().millisecondsSinceEpoch ~/ 1000) * 1000
            ),
          );
          marketDataList.add(marketData);
        } catch (e) {
          // Error parsing market data - silently handle
        }
      }
    });
    
    return marketDataList;
  }

  String _getCoinName(String coinId) {
    switch (coinId) {
      case 'bitcoin':
        return 'Bitcoin';
      case 'ethereum':
        return 'Ethereum';
      case 'binancecoin':
        return 'BNB';
      case 'cardano':
        return 'Cardano';
      case 'solana':
        return 'Solana';
      default:
        return coinId.toUpperCase();
    }
  }

  // Cache geçerli mi kontrol et
  bool _isCacheValid() {
    if (_lastUpdate == null || _cachedData.isEmpty) {
      return false;
    }
    return DateTime.now().difference(_lastUpdate!) < _cacheExpiry;
  }

  // Favori varlıkları yönet
  Future<void> addToFavorites(String assetId) async {
    if (!_favoriteAssets.contains(assetId)) {
      _favoriteAssets.add(assetId);
      await _saveFavoriteAssets();
    }
  }

  Future<void> removeFromFavorites(String assetId) async {
    _favoriteAssets.remove(assetId);
    await _saveFavoriteAssets();
  }

  // Local storage işlemleri
  Future<void> _loadFavoriteAssets() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getString('favorite_assets');
      if (favoritesJson != null) {
        final favoritesList = json.decode(favoritesJson) as List<dynamic>;
        _favoriteAssets = favoritesList.cast<String>();
      } else {
        _favoriteAssets = List.from(_defaultAssets);
      }
    } catch (e) {
      // Error loading favorite assets - silently handle
      _favoriteAssets = List.from(_defaultAssets);
    }
  }

  Future<void> _saveFavoriteAssets() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = json.encode(_favoriteAssets);
      await prefs.setString('favorite_assets', favoritesJson);
    } catch (e) {
      // Error saving favorite assets - silently handle
    }
  }

  Future<void> _loadCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_cacheKey);
      final lastUpdateTimestamp = prefs.getInt(_lastUpdateKey);
      
      if (cachedJson != null && lastUpdateTimestamp != null) {
        final cachedList = json.decode(cachedJson) as List<dynamic>;
        _cachedData = cachedList.map((item) => MarketData.fromJson(item)).toList();
        _lastUpdate = DateTime.fromMillisecondsSinceEpoch(lastUpdateTimestamp);
      }
    } catch (e) {
      // Error loading cached data - silently handle
    }
  }

  Future<void> _saveCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = json.encode(_cachedData.map((item) => item.toJson()).toList());
      await prefs.setString(_cacheKey, cachedJson);
      await prefs.setInt(_lastUpdateKey, _lastUpdate?.millisecondsSinceEpoch ?? 0);
    } catch (e) {
      // Error saving cached data - silently handle
    }
  }

  // Cache'i temizle
  Future<void> clearCache() async {
    _cachedData.clear();
    _lastUpdate = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_cacheKey);
    await prefs.remove(_lastUpdateKey);
  }
}
