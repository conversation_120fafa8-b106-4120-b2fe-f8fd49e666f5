class MarketData {
  final String id;
  final String symbol;
  final String name;
  final double currentPrice;
  final double priceChange24h;
  final double priceChangePercentage24h;
  final double marketCap;
  final double volume24h;
  final String image;
  final DateTime lastUpdated;
  final int? marketCapRank;
  final double? high24h;
  final double? low24h;
  final double? ath;
  final double? athChangePercentage;
  final DateTime? athDate;
  final double? circulatingSupply;
  final double? totalSupply;
  final double? maxSupply;

  MarketData({
    required this.id,
    required this.symbol,
    required this.name,
    required this.currentPrice,
    required this.priceChange24h,
    required this.priceChangePercentage24h,
    required this.marketCap,
    required this.volume24h,
    required this.image,
    required this.lastUpdated,
    this.marketCapRank,
    this.high24h,
    this.low24h,
    this.ath,
    this.athChangePercentage,
    this.athDate,
    this.circulatingSupply,
    this.totalSupply,
    this.maxSupply,
  });

  factory MarketData.fromJson(Map<String, dynamic> json) {
    return MarketData(
      id: json['id'] ?? '',
      symbol: json['symbol'] ?? '',
      name: json['name'] ?? '',
      currentPrice: (json['current_price'] ?? 0).toDouble(),
      priceChange24h: (json['price_change_24h'] ?? 0).toDouble(),
      priceChangePercentage24h: (json['price_change_percentage_24h'] ?? 0).toDouble(),
      marketCap: (json['market_cap'] ?? 0).toDouble(),
      volume24h: (json['total_volume'] ?? 0).toDouble(),
      image: json['image'] ?? '',
      lastUpdated: DateTime.tryParse(json['last_updated'] ?? '') ?? DateTime.now(),
      marketCapRank: json['market_cap_rank']?.toInt(),
      high24h: json['high_24h']?.toDouble(),
      low24h: json['low_24h']?.toDouble(),
      ath: json['ath']?.toDouble(),
      athChangePercentage: json['ath_change_percentage']?.toDouble(),
      athDate: json['ath_date'] != null ? DateTime.tryParse(json['ath_date']) : null,
      circulatingSupply: json['circulating_supply']?.toDouble(),
      totalSupply: json['total_supply']?.toDouble(),
      maxSupply: json['max_supply']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'symbol': symbol,
      'name': name,
      'current_price': currentPrice,
      'price_change_24h': priceChange24h,
      'price_change_percentage_24h': priceChangePercentage24h,
      'market_cap': marketCap,
      'total_volume': volume24h,
      'image': image,
      'last_updated': lastUpdated.toIso8601String(),
      'market_cap_rank': marketCapRank,
      'high_24h': high24h,
      'low_24h': low24h,
      'ath': ath,
      'ath_change_percentage': athChangePercentage,
      'ath_date': athDate?.toIso8601String(),
      'circulating_supply': circulatingSupply,
      'total_supply': totalSupply,
      'max_supply': maxSupply,
    };
  }

  bool get isPriceUp => priceChangePercentage24h > 0;
  bool get isPositiveChange => priceChangePercentage24h >= 0;

  String get formattedPrice {
    if (currentPrice >= 1) {
      return '\$${currentPrice.toStringAsFixed(2)}';
    } else {
      return '\$${currentPrice.toStringAsFixed(6)}';
    }
  }

  String get formattedMarketCap {
    if (marketCap >= 1e12) {
      return '\$${(marketCap / 1e12).toStringAsFixed(2)}T';
    } else if (marketCap >= 1e9) {
      return '\$${(marketCap / 1e9).toStringAsFixed(2)}B';
    } else if (marketCap >= 1e6) {
      return '\$${(marketCap / 1e6).toStringAsFixed(2)}M';
    } else {
      return '\$${marketCap.toStringAsFixed(0)}';
    }
  }

  String get formattedVolume {
    if (volume24h >= 1e9) {
      return '\$${(volume24h / 1e9).toStringAsFixed(2)}B';
    } else if (volume24h >= 1e6) {
      return '\$${(volume24h / 1e6).toStringAsFixed(2)}M';
    } else {
      return '\$${volume24h.toStringAsFixed(0)}';
    }
  }

  @override
  String toString() {
    return 'MarketData(id: $id, symbol: $symbol, name: $name, currentPrice: $currentPrice, priceChangePercentage24h: $priceChangePercentage24h%)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MarketData &&
        other.id == id &&
        other.symbol == symbol &&
        other.name == name &&
        other.currentPrice == currentPrice;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        symbol.hashCode ^
        name.hashCode ^
        currentPrice.hashCode;
  }
}
