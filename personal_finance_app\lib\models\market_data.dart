class MarketData {
  final String symbol;
  final String name;
  final double currentPrice;
  final double priceChange24h;
  final double priceChangePercentage24h;
  final DateTime lastUpdated;

  MarketData({
    required this.symbol,
    required this.name,
    required this.currentPrice,
    required this.priceChange24h,
    required this.priceChangePercentage24h,
    required this.lastUpdated,
  });

  factory MarketData.fromJson(Map<String, dynamic> json) {
    return MarketData(
      symbol: json['symbol'] ?? '',
      name: json['name'] ?? '',
      currentPrice: (json['current_price'] ?? 0).toDouble(),
      priceChange24h: (json['price_change_24h'] ?? 0).toDouble(),
      priceChangePercentage24h: (json['price_change_percentage_24h'] ?? 0).toDouble(),
      lastUpdated: DateTime.parse(json['last_updated'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'name': name,
      'current_price': currentPrice,
      'price_change_24h': priceChange24h,
      'price_change_percentage_24h': priceChangePercentage24h,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }

  bool get isPositiveChange => priceChangePercentage24h >= 0;

  @override
  String toString() {
    return 'MarketData(symbol: $symbol, name: $name, currentPrice: $currentPrice, priceChangePercentage24h: $priceChangePercentage24h%)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MarketData &&
        other.symbol == symbol &&
        other.name == name &&
        other.currentPrice == currentPrice &&
        other.priceChange24h == priceChange24h &&
        other.priceChangePercentage24h == priceChangePercentage24h &&
        other.lastUpdated == lastUpdated;
  }

  @override
  int get hashCode {
    return symbol.hashCode ^
        name.hashCode ^
        currentPrice.hashCode ^
        priceChange24h.hashCode ^
        priceChangePercentage24h.hashCode ^
        lastUpdated.hashCode;
  }
}
