{"buildFiles": ["C:\\DEV\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\asd\\personal_finance_app\\build\\.cxx\\Debug\\2n440114\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\asd\\personal_finance_app\\build\\.cxx\\Debug\\2n440114\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}