import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../models/transaction.dart' as app_models;
import '../../models/account.dart';
import '../../models/category.dart';
import '../../services/auth_service.dart';
import '../../services/database_service.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';

class AddTransactionScreen extends StatefulWidget {
  final List<Account> accounts;
  
  const AddTransactionScreen({
    super.key,
    required this.accounts,
  });

  @override
  State<AddTransactionScreen> createState() => _AddTransactionScreenState();
}

class _AddTransactionScreenState extends State<AddTransactionScreen> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final DatabaseService _databaseService = DatabaseService();
  
  bool _isLoading = false;
  app_models.TransactionType _selectedType = app_models.TransactionType.expense;
  Account? _selectedAccount;
  String? _selectedCategory;
  DateTime _selectedDate = DateTime.now();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    if (widget.accounts.isNotEmpty) {
      _selectedAccount = widget.accounts.first;
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  List<Category> get _availableCategories {
    return _selectedType == app_models.TransactionType.income
        ? DefaultCategories.incomeCategories
        : DefaultCategories.expenseCategories;
  }

  Future<void> _saveTransaction() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedAccount == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Lütfen bir hesap seçin')),
      );
      return;
    }

    if (_selectedCategory == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Lütfen bir kategori seçin')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final user = authService.currentUser;
      
      if (user != null) {
        final transaction = app_models.Transaction(
          userId: user.id!,
          accountId: _selectedAccount!.id,
          type: _selectedType,
          amount: double.parse(_amountController.text.replaceAll(',', '.')),
          category: _selectedCategory!,
          description: _descriptionController.text.trim().isEmpty 
              ? null 
              : _descriptionController.text.trim(),
          date: _selectedDate,
          createdAt: DateTime.now(),
        );

        await _databaseService.insertTransaction(transaction);
        
        // Hesap bakiyesini güncelle
        final newBalance = _selectedType == app_models.TransactionType.income
            ? _selectedAccount!.balance + transaction.amount
            : _selectedAccount!.balance - transaction.amount;
            
        final updatedAccount = _selectedAccount!.copyWith(
          balance: newBalance,
          updatedAt: DateTime.now(),
        );
        
        await _databaseService.updateAccount(updatedAccount);
        
        if (mounted) {
          Navigator.of(context).pop(true);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('İşlem başarıyla eklendi')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('İşlem eklenirken hata oluştu')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      locale: const Locale('tr', 'TR'),
    );
    
    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'İşlem Ekle',
          style: TextStyle(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.blue,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Colors.blue,
          onTap: (index) {
            setState(() {
              _selectedType = index == 0 
                  ? app_models.TransactionType.expense 
                  : app_models.TransactionType.income;
              _selectedCategory = null; // Reset category when type changes
            });
          },
          tabs: const [
            Tab(
              icon: Icon(Icons.trending_down),
              text: 'Gider',
            ),
            Tab(
              icon: Icon(Icons.trending_up),
              text: 'Gelir',
            ),
          ],
        ),
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(24),
          children: [
            // Miktar
            CustomTextField(
              controller: _amountController,
              labelText: 'Miktar',
              hintText: '0,00',
              prefixIcon: Icons.attach_money,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[0-9,.-]')),
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Miktar gerekli';
                }
                final amount = double.tryParse(value.replaceAll(',', '.'));
                if (amount == null || amount <= 0) {
                  return 'Geçerli bir miktar girin';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Hesap seçimi
            if (widget.accounts.isNotEmpty) ...[
              const Text(
                'Hesap',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<Account>(
                    value: _selectedAccount,
                    isExpanded: true,
                    items: widget.accounts.map((account) {
                      return DropdownMenuItem(
                        value: account,
                        child: Text(
                          '${account.bankName} - ${account.accountName}',
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.black87,
                          ),
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedAccount = value;
                      });
                    },
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Kategori seçimi
            const Text(
              'Kategori',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 120,
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 4,
                  childAspectRatio: 1,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: _availableCategories.length,
                itemBuilder: (context, index) {
                  final category = _availableCategories[index];
                  final isSelected = _selectedCategory == category.name;
                  
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedCategory = category.name;
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected ? Colors.blue.shade50 : Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected ? Colors.blue : Colors.grey.shade300,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            category.icon,
                            style: const TextStyle(fontSize: 20),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            category.name,
                            style: TextStyle(
                              fontSize: 10,
                              color: isSelected ? Colors.blue : Colors.black87,
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),

            // Tarih seçimi
            CustomTextField(
              controller: TextEditingController(
                text: DateFormat('dd MMMM yyyy', 'tr_TR').format(_selectedDate),
              ),
              labelText: 'Tarih',
              prefixIcon: Icons.calendar_today,
              readOnly: true,
              onTap: _selectDate,
            ),
            const SizedBox(height: 16),

            // Açıklama
            CustomTextField(
              controller: _descriptionController,
              labelText: 'Açıklama (Opsiyonel)',
              hintText: 'İşlem hakkında not ekleyin',
              prefixIcon: Icons.note,
              maxLines: 3,
            ),
            const SizedBox(height: 32),

            // Kaydet butonu
            CustomButton(
              text: 'İşlemi Kaydet',
              onPressed: _isLoading ? null : _saveTransaction,
              isLoading: _isLoading,
            ),
          ],
        ),
      ),
    );
  }
}
