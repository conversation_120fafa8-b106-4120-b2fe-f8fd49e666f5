class Account {
  final int? id;
  final int userId;
  final String bankName;
  final String accountName;
  final String? description;
  final double balance;
  final String currency;
  final DateTime createdAt;
  final DateTime updatedAt;

  Account({
    this.id,
    required this.userId,
    required this.bankName,
    required this.accountName,
    this.description,
    required this.balance,
    this.currency = 'TRY',
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'bank_name': bankName,
      'account_name': accountName,
      'description': description,
      'balance': balance,
      'currency': currency,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory Account.fromMap(Map<String, dynamic> map) {
    return Account(
      id: map['id']?.toInt(),
      userId: map['user_id']?.toInt() ?? 0,
      bankName: map['bank_name'] ?? '',
      accountName: map['account_name'] ?? '',
      description: map['description'],
      balance: map['balance']?.toDouble() ?? 0.0,
      currency: map['currency'] ?? 'TRY',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }

  Account copyWith({
    int? id,
    int? userId,
    String? bankName,
    String? accountName,
    String? description,
    double? balance,
    String? currency,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Account(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      bankName: bankName ?? this.bankName,
      accountName: accountName ?? this.accountName,
      description: description ?? this.description,
      balance: balance ?? this.balance,
      currency: currency ?? this.currency,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Account(id: $id, bankName: $bankName, accountName: $accountName, balance: $balance, currency: $currency)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Account &&
        other.id == id &&
        other.userId == userId &&
        other.bankName == bankName &&
        other.accountName == accountName &&
        other.description == description &&
        other.balance == balance &&
        other.currency == currency &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        bankName.hashCode ^
        accountName.hashCode ^
        description.hashCode ^
        balance.hashCode ^
        currency.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }
}
