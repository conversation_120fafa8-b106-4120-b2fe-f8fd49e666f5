// ignore_for_file: deprecated_member_use
import 'package:flutter/material.dart';

class Category {
  final int? id;
  final String name;
  final String icon;
  final Color color;
  final bool isDefault;

  Category({
    this.id,
    required this.name,
    required this.icon,
    required this.color,
    this.isDefault = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'icon': icon,
      'color': color.value, // Database için integer gerekli
      'is_default': isDefault ? 1 : 0,
    };
  }

  factory Category.fromMap(Map<String, dynamic> map) {
    return Category(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      icon: map['icon'] ?? '',
      color: Color(map['color'] ?? 0xFF2196F3), // Colors.blue'nun değeri
      isDefault: (map['is_default'] ?? 0) == 1,
    );
  }

  Category copyWith({
    int? id,
    String? name,
    String? icon,
    Color? color,
    bool? isDefault,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isDefault: isDefault ?? this.isDefault,
    );
  }

  @override
  String toString() {
    return 'Category(id: $id, name: $name, icon: $icon, isDefault: $isDefault)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Category &&
        other.id == id &&
        other.name == name &&
        other.icon == icon &&
        other.color == color &&
        other.isDefault == isDefault;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        icon.hashCode ^
        color.hashCode ^
        isDefault.hashCode;
  }
}

// Varsayılan kategoriler
class DefaultCategories {
  static List<Category> get expenseCategories => [
    Category(name: 'Gıda', icon: '🍽️', color: Colors.orange),
    Category(name: 'Ulaşım', icon: '🚗', color: Colors.blue),
    Category(name: 'Kira', icon: '🏠', color: Colors.green),
    Category(name: 'Faturalar', icon: '💡', color: Colors.yellow),
    Category(name: 'Sağlık', icon: '🏥', color: Colors.red),
    Category(name: 'Eğlence', icon: '🎬', color: Colors.purple),
    Category(name: 'Alışveriş', icon: '🛍️', color: Colors.pink),
    Category(name: 'Diğer', icon: '📦', color: Colors.grey),
  ];

  static List<Category> get incomeCategories => [
    Category(name: 'Maaş', icon: '💰', color: Colors.green),
    Category(name: 'Freelance', icon: '💻', color: Colors.blue),
    Category(name: 'Yatırım', icon: '📈', color: Colors.teal),
    Category(name: 'Hediye', icon: '🎁', color: Colors.pink),
    Category(name: 'Diğer', icon: '📦', color: Colors.grey),
  ];
}
