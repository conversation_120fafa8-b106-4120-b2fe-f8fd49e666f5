import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../services/realtime_market_service.dart';
import '../../models/market_data.dart';
import 'coin_detail_screen.dart';

class RealtimeMarketScreen extends StatefulWidget {
  const RealtimeMarketScreen({super.key});

  @override
  State<RealtimeMarketScreen> createState() => _RealtimeMarketScreenState();
}

class _RealtimeMarketScreenState extends State<RealtimeMarketScreen>
    with TickerProviderStateMixin {
  final RealtimeMarketService _marketService = RealtimeMarketService();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  List<MarketData> _marketData = [];
  List<MarketData> _filteredData = [];
  List<MarketData> _trendingCoins = [];
  bool _isLoading = true;
  bool _isConnected = false;
  bool _isSearching = false;
  String _sortBy = 'market_cap_desc';
  DateTime? _lastUpdate;
  
  late AnimationController _animationController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _pulseAnimation;

  // Price change animations
  final Map<String, AnimationController> _priceAnimations = {};
  final Map<String, Animation<Color?>> _priceColorAnimations = {};

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeData();
    _setupSearchListener();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.elasticOut),
    );
    
    _pulseController.repeat(reverse: true);
  }

  void _initializeData() async {
    await _marketService.initialize();
    
    // Market data stream'ini dinle
    _marketService.marketDataStream.listen((data) {
      if (mounted) {
        setState(() {
          _marketData = data;
          _filteredData = _isSearching ? _filteredData : data;
          _isLoading = false;
          _lastUpdate = DateTime.now();
        });
        _animationController.forward();
        _updateTrendingCoins();
      }
    });

    // Connection stream'ini dinle
    _marketService.connectionStream.listen((isConnected) {
      if (mounted) {
        setState(() {
          _isConnected = isConnected;
        });
      }
    });

    // Price change stream'ini dinle
    _marketService.priceChangeStream.listen((changes) {
      if (mounted) {
        _animatePriceChanges(changes);
      }
    });
  }

  void _updateTrendingCoins() {
    _trendingCoins = _marketService.getTrendingCoins();
  }

  void _animatePriceChanges(Map<String, double> changes) {
    for (final entry in changes.entries) {
      final symbol = entry.key;
      final change = entry.value;
      
      // Mevcut animasyonu temizle
      _priceAnimations[symbol]?.dispose();
      
      // Yeni animasyon oluştur
      final controller = AnimationController(
        duration: const Duration(milliseconds: 800),
        vsync: this,
      );
      
      final colorAnimation = ColorTween(
        begin: change > 0 ? Colors.green.withValues(alpha: 0.3) : Colors.red.withValues(alpha: 0.3),
        end: Colors.transparent,
      ).animate(CurvedAnimation(parent: controller, curve: Curves.easeOut));
      
      _priceAnimations[symbol] = controller;
      _priceColorAnimations[symbol] = colorAnimation;
      
      controller.forward().then((_) {
        controller.dispose();
        _priceAnimations.remove(symbol);
        _priceColorAnimations.remove(symbol);
      });
    }
  }

  void _setupSearchListener() {
    _searchController.addListener(() {
      _performSearch(_searchController.text);
    });
  }

  void _performSearch(String query) {
    setState(() {
      _isSearching = query.isNotEmpty;
      _filteredData = _marketService.searchCoins(query);
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pulseController.dispose();
    _searchController.dispose();
    _scrollController.dispose();
    
    // Price animation'ları temizle
    for (final controller in _priceAnimations.values) {
      controller.dispose();
    }
    
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            if (_trendingCoins.isNotEmpty) _buildTrendingSection(),
            _buildSearchBar(),
            _buildSortOptions(),
            Expanded(child: _buildMarketList()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.blue.shade600, Colors.blue.shade800],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Canlı Piyasa',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Row(
                children: [
                  // Real-time indicator
                  ScaleTransition(
                    scale: _pulseAnimation,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: _isConnected ? Colors.green : Colors.red,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: (_isConnected ? Colors.green : Colors.red).withValues(alpha: 0.5),
                            blurRadius: 8,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _isConnected ? 'CANLI' : 'BAĞLANTI YOK',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: _isLoading ? null : () async {
                      await _marketService.forceRefresh();
                    },
                    icon: _isLoading 
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Icon(Icons.refresh, color: Colors.white),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                'Son güncelleme: ${_lastUpdate != null ? DateFormat('HH:mm:ss').format(_lastUpdate!) : 'Yükleniyor...'}',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 14,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '• ${_marketData.length} coin',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 14,
                ),
              ),
              if (_isConnected) ...[
                const SizedBox(width: 8),
                Text(
                  '• Gerçek zamanlı',
                  style: TextStyle(
                    color: Colors.green.shade200,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTrendingSection() {
    return Container(
      height: 120,
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                const Text(
                  'Trend Coinler',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  Icons.trending_up,
                  color: Colors.green.shade600,
                  size: 20,
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _trendingCoins.length,
              itemBuilder: (context, index) {
                final coin = _trendingCoins[index];
                return _buildTrendingCard(coin);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrendingCard(MarketData coin) {
    final hasAnimation = _priceColorAnimations.containsKey(coin.symbol.toUpperCase());
    final animation = hasAnimation ? _priceColorAnimations[coin.symbol.toUpperCase()] : null;

    return AnimatedBuilder(
      animation: animation ?? const AlwaysStoppedAnimation(Colors.transparent),
      builder: (context, child) {
        return Container(
          width: 140,
          margin: const EdgeInsets.symmetric(horizontal: 4),
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: hasAnimation && animation != null
                ? animation.value ?? Colors.white
                : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: coin.isPriceUp
                ? Border.all(color: Colors.green.withValues(alpha: 0.3), width: 1)
                : coin.priceChangePercentage24h < 0
                    ? Border.all(color: Colors.red.withValues(alpha: 0.3), width: 1)
                    : null,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 10,
                    backgroundImage: coin.image.isNotEmpty
                        ? NetworkImage(coin.image)
                        : null,
                    backgroundColor: Colors.grey[300],
                    child: coin.image.isEmpty
                        ? Text(
                            coin.symbol.substring(0, 1).toUpperCase(),
                            style: const TextStyle(fontSize: 10),
                          )
                        : null,
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      coin.symbol.toUpperCase(),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 11,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (_isConnected)
                    Container(
                      width: 6,
                      height: 6,
                      decoration: BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                coin.formattedPrice,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 13,
                  color: hasAnimation
                      ? (coin.isPriceUp ? Colors.green.shade700 : Colors.red.shade700)
                      : Colors.black87,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              Row(
                children: [
                  Icon(
                    coin.isPriceUp ? Icons.trending_up : Icons.trending_down,
                    size: 10,
                    color: coin.isPriceUp ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 2),
                  Text(
                    '${coin.priceChangePercentage24h.toStringAsFixed(1)}%',
                    style: TextStyle(
                      color: coin.isPriceUp ? Colors.green : Colors.red,
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Coin ara... (gerçek zamanlı)',
          prefixIcon: Icon(Icons.search, color: Colors.grey),
          suffixIcon: _isConnected
              ? Icon(Icons.wifi, color: Colors.green, size: 16)
              : Icon(Icons.wifi_off, color: Colors.red, size: 16),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),
    );
  }

  Widget _buildSortOptions() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildSortChip('Piyasa Değeri', 'market_cap_desc'),
          _buildSortChip('Fiyat', 'price_desc'),
          _buildSortChip('24s Değişim', 'price_change_percentage_24h_desc'),
          _buildSortChip('Hacim', 'volume_desc'),
          _buildSortChip('İsim', 'name_asc'),
        ],
      ),
    );
  }

  Widget _buildSortChip(String label, String value) {
    final isSelected = _sortBy == value;
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _sortBy = value;
          });
          _sortData();
        },
        selectedColor: Colors.blue.withValues(alpha: 0.2),
        checkmarkColor: Colors.blue,
      ),
    );
  }

  void _sortData() {
    setState(() {
      switch (_sortBy) {
        case 'market_cap_desc':
          _filteredData.sort((a, b) => b.marketCap.compareTo(a.marketCap));
          break;
        case 'price_desc':
          _filteredData.sort((a, b) => b.currentPrice.compareTo(a.currentPrice));
          break;
        case 'price_change_percentage_24h_desc':
          _filteredData.sort((a, b) => b.priceChangePercentage24h.compareTo(a.priceChangePercentage24h));
          break;
        case 'volume_desc':
          _filteredData.sort((a, b) => b.volume24h.compareTo(a.volume24h));
          break;
        case 'name_asc':
          _filteredData.sort((a, b) => a.name.compareTo(b.name));
          break;
      }
    });
  }

  Widget _buildMarketList() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Gerçek zamanlı veriler yükleniyor...'),
          ],
        ),
      );
    }

    if (_filteredData.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _isSearching ? 'Arama sonucu bulunamadı' : 'Market verisi bulunamadı',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: RefreshIndicator(
        onRefresh: () async {
          await _marketService.forceRefresh();
        },
        child: ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          itemCount: _filteredData.length,
          itemBuilder: (context, index) {
            final coin = _filteredData[index];
            return _buildRealtimeCoinCard(coin, index);
          },
        ),
      ),
    );
  }

  Widget _buildRealtimeCoinCard(MarketData coin, int index) {
    final hasAnimation = _priceColorAnimations.containsKey(coin.symbol.toUpperCase());
    final animation = hasAnimation ? _priceColorAnimations[coin.symbol.toUpperCase()] : null;
    final isRecentlyUpdated = _lastUpdate != null &&
        DateTime.now().difference(_lastUpdate!).inSeconds < 2;

    return AnimatedBuilder(
      animation: animation ?? const AlwaysStoppedAnimation(Colors.transparent),
      builder: (context, child) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: hasAnimation && animation != null
                ? animation.value ?? Colors.white
                : Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: isRecentlyUpdated
                ? Border.all(color: Colors.blue.withValues(alpha: 0.3), width: 2)
                : coin.isPriceUp
                    ? Border.all(color: Colors.green.withValues(alpha: 0.3), width: 1)
                    : coin.priceChangePercentage24h < 0
                        ? Border.all(color: Colors.red.withValues(alpha: 0.3), width: 1)
                        : null,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(16),
              onTap: () => _navigateToCoinDetail(coin),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // Rank & Live indicator
                    Column(
                      children: [
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: Text(
                              '${coin.marketCapRank ?? index + 1}',
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 4),
                        if (_isConnected)
                          Container(
                            width: 6,
                            height: 6,
                            decoration: BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(width: 12),

                    // Coin Image & Info
                    Expanded(
                      flex: 3,
                      child: Row(
                        children: [
                          CircleAvatar(
                            radius: 20,
                            backgroundImage: coin.image.isNotEmpty
                                ? NetworkImage(coin.image)
                                : null,
                            backgroundColor: Colors.grey[300],
                            child: coin.image.isEmpty
                                ? Text(
                                    coin.symbol.substring(0, 1).toUpperCase(),
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  )
                                : null,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  coin.name,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                                Row(
                                  children: [
                                    Text(
                                      coin.symbol.toUpperCase(),
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                        fontSize: 14,
                                      ),
                                    ),
                                    if (coin.volume24h > 0) ...[
                                      const SizedBox(width: 8),
                                      Text(
                                        coin.formattedVolume,
                                        style: TextStyle(
                                          color: Colors.grey[500],
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Price & Change with real-time animation
                    Expanded(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          AnimatedDefaultTextStyle(
                            duration: const Duration(milliseconds: 300),
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                              color: hasAnimation
                                  ? (coin.isPriceUp ? Colors.green.shade700 : Colors.red.shade700)
                                  : Colors.black87,
                            ),
                            child: Text(coin.formattedPrice),
                          ),
                          const SizedBox(height: 4),
                          AnimatedContainer(
                            duration: const Duration(milliseconds: 300),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: coin.isPriceUp
                                  ? Colors.green.withValues(alpha: 0.15)
                                  : Colors.red.withValues(alpha: 0.15),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  coin.isPriceUp ? Icons.trending_up : Icons.trending_down,
                                  size: 12,
                                  color: coin.isPriceUp ? Colors.green : Colors.red,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  '${coin.isPriceUp ? '+' : ''}${coin.priceChangePercentage24h.toStringAsFixed(2)}%',
                                  style: TextStyle(
                                    color: coin.isPriceUp ? Colors.green : Colors.red,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _navigateToCoinDetail(MarketData coin) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CoinDetailScreen(coin: coin),
      ),
    );
  }
}
