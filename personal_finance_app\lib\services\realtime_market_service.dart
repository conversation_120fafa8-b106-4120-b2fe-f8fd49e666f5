import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/market_data.dart';

class RealtimeMarketService {
  static final RealtimeMarketService _instance = RealtimeMarketService._internal();
  factory RealtimeMarketService() => _instance;
  RealtimeMarketService._internal();

  static const String _coinGeckoUrl = 'https://api.coingecko.com/api/v3';

  Timer? _priceUpdateTimer;
  bool _isConnected = false;
  
  final Map<String, MarketData> _marketDataMap = {};
  final Map<String, double> _previousPrices = {};
  final Map<String, DateTime> _lastPriceUpdate = {};
  
  final StreamController<List<MarketData>> _marketDataController = 
      StreamController<List<MarketData>>.broadcast();
  final StreamController<bool> _connectionController = 
      StreamController<bool>.broadcast();
  final StreamController<Map<String, double>> _priceChangeController = 
      StreamController<Map<String, double>>.broadcast();
  
  Stream<List<MarketData>> get marketDataStream => _marketDataController.stream;
  Stream<bool> get connectionStream => _connectionController.stream;
  Stream<Map<String, double>> get priceChangeStream => _priceChangeController.stream;
  
  bool get isConnected => _isConnected;
  List<MarketData> get currentMarketData => _marketDataMap.values.toList();

  Future<void> initialize() async {
    await _loadInitialData();
    _connectWebSocket();
    _startPriceUpdateTimer();
  }

  Future<void> _loadInitialData() async {
    try {
      // İlk veri yüklemesi için CoinGecko API kullan
      final response = await http.get(
        Uri.parse('$_coinGeckoUrl/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=100&page=1&sparkline=false&price_change_percentage=24h'),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        
        for (final coinData in data) {
          final marketData = MarketData.fromJson(coinData);
          _marketDataMap[marketData.symbol.toUpperCase()] = marketData;
          _previousPrices[marketData.symbol.toUpperCase()] = marketData.currentPrice;
        }
        
        _marketDataController.add(_marketDataMap.values.toList());
      }
    } catch (e) {
      // Hata durumunda cache'den yükle
      await _loadCachedData();
    }
  }

  void _connectWebSocket() {
    // Simülasyon için WebSocket yerine Timer kullan
    _isConnected = true;
    _connectionController.add(true);

    // Gerçek zamanlı fiyat simülasyonu
    Timer.periodic(const Duration(seconds: 2), (timer) {
      _simulatePriceUpdates();
    });
  }

  void _simulatePriceUpdates() {
    final Map<String, double> priceChanges = {};
    final random = Random();

    // Her coin için rastgele fiyat değişimi simüle et
    for (final entry in _marketDataMap.entries) {
      final symbol = entry.key;
      final coin = entry.value;

      // %0.1 ile %2 arasında rastgele değişim
      final changePercent = (random.nextDouble() - 0.5) * 4; // -2% ile +2% arası
      final priceChange = coin.currentPrice * (changePercent / 100);
      final newPrice = coin.currentPrice + priceChange;

      if (newPrice > 0) {
        final previousPrice = _previousPrices[symbol] ?? coin.currentPrice;

        // Fiyat değişimini kaydet
        priceChanges[symbol] = newPrice - previousPrice;
        _previousPrices[symbol] = newPrice;
        _lastPriceUpdate[symbol] = DateTime.now();

        // MarketData'yı güncelle
        final updatedData = MarketData(
          id: coin.id,
          symbol: coin.symbol,
          name: coin.name,
          currentPrice: newPrice,
          priceChange24h: coin.priceChange24h + priceChange,
          priceChangePercentage24h: coin.priceChangePercentage24h + changePercent,
          marketCap: coin.marketCap * (newPrice / coin.currentPrice),
          volume24h: coin.volume24h * (0.9 + random.nextDouble() * 0.2), // Volume değişimi
          image: coin.image,
          lastUpdated: DateTime.now(),
          marketCapRank: coin.marketCapRank,
          high24h: coin.high24h != null ?
              (newPrice > coin.high24h! ? newPrice : coin.high24h) : newPrice,
          low24h: coin.low24h != null ?
              (newPrice < coin.low24h! ? newPrice : coin.low24h) : newPrice,
          ath: coin.ath,
          athChangePercentage: coin.athChangePercentage,
          athDate: coin.athDate,
          circulatingSupply: coin.circulatingSupply,
          totalSupply: coin.totalSupply,
          maxSupply: coin.maxSupply,
        );

        _marketDataMap[symbol] = updatedData;
      }
    }

    // Fiyat değişimlerini yayınla
    if (priceChanges.isNotEmpty) {
      _priceChangeController.add(priceChanges);
    }
  }

  void _startPriceUpdateTimer() {
    _priceUpdateTimer?.cancel();
    _priceUpdateTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      // Her 500ms'de market data'yı yayınla
      _marketDataController.add(_marketDataMap.values.toList());
    });
  }

  // Trending coins için simülasyon
  List<MarketData> getTrendingCoins() {
    final allCoins = _marketDataMap.values.toList();
    allCoins.sort((a, b) => b.priceChangePercentage24h.compareTo(a.priceChangePercentage24h));
    return allCoins.take(7).toList();
  }

  // Arama fonksiyonu
  List<MarketData> searchCoins(String query) {
    if (query.isEmpty) return _marketDataMap.values.toList();
    
    return _marketDataMap.values.where((coin) =>
      coin.name.toLowerCase().contains(query.toLowerCase()) ||
      coin.symbol.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }

  // Coin detayı
  MarketData? getCoinDetails(String coinId) {
    return _marketDataMap.values.firstWhere(
      (coin) => coin.id == coinId || coin.symbol.toLowerCase() == coinId.toLowerCase(),
      orElse: () => _marketDataMap.values.first,
    );
  }

  // Cache işlemleri
  Future<void> _loadCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString('realtime_market_cache');

      if (cachedData != null) {
        final List<dynamic> data = json.decode(cachedData);
        for (final coinData in data) {
          final marketData = MarketData.fromJson(coinData);
          _marketDataMap[marketData.symbol.toUpperCase()] = marketData;
        }
      }
    } catch (e) {
      // Cache yükleme hatası
    }
  }

  // Bağlantı durumu kontrolü
  Future<bool> checkConnection() async {
    return _isConnected;
  }

  // Manuel yenileme
  Future<void> forceRefresh() async {
    await _loadInitialData();
  }

  // Temizlik
  void dispose() {
    _priceUpdateTimer?.cancel();
    _marketDataController.close();
    _connectionController.close();
    _priceChangeController.close();
  }
}
