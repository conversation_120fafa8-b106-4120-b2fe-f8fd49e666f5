import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/market_data.dart';

class RealtimeMarketService {
  static final RealtimeMarketService _instance = RealtimeMarketService._internal();
  factory RealtimeMarketService() => _instance;
  RealtimeMarketService._internal();

  static const String _coinGeckoUrl = 'https://api.coingecko.com/api/v3';

  Timer? _priceUpdateTimer;
  bool _isConnected = false;
  
  final Map<String, MarketData> _marketDataMap = {};
  final Map<String, double> _previousPrices = {};
  final Map<String, DateTime> _lastPriceUpdate = {};
  
  final StreamController<List<MarketData>> _marketDataController = 
      StreamController<List<MarketData>>.broadcast();
  final StreamController<bool> _connectionController = 
      StreamController<bool>.broadcast();
  final StreamController<Map<String, double>> _priceChangeController = 
      StreamController<Map<String, double>>.broadcast();
  
  Stream<List<MarketData>> get marketDataStream => _marketDataController.stream;
  Stream<bool> get connectionStream => _connectionController.stream;
  Stream<Map<String, double>> get priceChangeStream => _priceChangeController.stream;
  
  bool get isConnected => _isConnected;
  List<MarketData> get currentMarketData => _marketDataMap.values.toList();

  Future<void> initialize() async {
    await _loadInitialData();
    _connectWebSocket();
    _startPriceUpdateTimer();
  }

  Future<void> _loadInitialData() async {
    try {
      // İlk veri yüklemesi için CoinGecko API kullan
      final response = await http.get(
        Uri.parse('$_coinGeckoUrl/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=100&page=1&sparkline=false&price_change_percentage=24h'),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        
        for (final coinData in data) {
          final marketData = MarketData.fromJson(coinData);
          _marketDataMap[marketData.symbol.toUpperCase()] = marketData;
          _previousPrices[marketData.symbol.toUpperCase()] = marketData.currentPrice;
        }
        
        _marketDataController.add(_marketDataMap.values.toList());
      }
    } catch (e) {
      // Hata durumunda cache'den yükle
      await _loadCachedData();
    }
  }

  void _connectWebSocket() {
    // Gerçek API'den canlı veri al
    _isConnected = true;
    _connectionController.add(true);

    // Her 10 saniyede bir gerçek API'den veri al
    Timer.periodic(const Duration(seconds: 10), (timer) async {
      await _fetchRealTimeData();
    });
  }

  Future<void> _fetchRealTimeData() async {
    try {
      // CoinGecko API'den güncel verileri al
      final response = await http.get(
        Uri.parse('$_coinGeckoUrl/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=100&page=1&sparkline=false&price_change_percentage=24h'),
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'PersonalFinanceApp/1.0',
        },
      ).timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final Map<String, double> priceChanges = {};

        for (final coinData in data) {
          final newMarketData = MarketData.fromJson(coinData);
          final symbol = newMarketData.symbol.toUpperCase();

          // Önceki fiyatla karşılaştır
          if (_marketDataMap.containsKey(symbol)) {
            final oldPrice = _marketDataMap[symbol]!.currentPrice;
            final newPrice = newMarketData.currentPrice;

            if (oldPrice != newPrice) {
              priceChanges[symbol] = newPrice - oldPrice;
              _previousPrices[symbol] = newPrice;
              _lastPriceUpdate[symbol] = DateTime.now();
            }
          } else {
            _previousPrices[symbol] = newMarketData.currentPrice;
          }

          _marketDataMap[symbol] = newMarketData;
        }

        // Fiyat değişimlerini yayınla
        if (priceChanges.isNotEmpty) {
          _priceChangeController.add(priceChanges);
        }

        // Market data'yı yayınla
        _marketDataController.add(_marketDataMap.values.toList());

      } else if (response.statusCode == 429) {
        // Rate limit - biraz bekle
        await Future.delayed(const Duration(seconds: 30));
      }
    } catch (e) {
      // API hatası - bağlantı durumunu güncelle
      _isConnected = false;
      _connectionController.add(false);

      // 30 saniye sonra tekrar dene
      Timer(const Duration(seconds: 30), () {
        _isConnected = true;
        _connectionController.add(true);
      });
    }
  }

  void _startPriceUpdateTimer() {
    _priceUpdateTimer?.cancel();
    _priceUpdateTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      // Her 500ms'de market data'yı yayınla
      _marketDataController.add(_marketDataMap.values.toList());
    });
  }

  // Trending coins için simülasyon
  List<MarketData> getTrendingCoins() {
    final allCoins = _marketDataMap.values.toList();
    allCoins.sort((a, b) => b.priceChangePercentage24h.compareTo(a.priceChangePercentage24h));
    return allCoins.take(7).toList();
  }

  // Arama fonksiyonu
  List<MarketData> searchCoins(String query) {
    if (query.isEmpty) return _marketDataMap.values.toList();
    
    return _marketDataMap.values.where((coin) =>
      coin.name.toLowerCase().contains(query.toLowerCase()) ||
      coin.symbol.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }

  // Coin detayı
  MarketData? getCoinDetails(String coinId) {
    return _marketDataMap.values.firstWhere(
      (coin) => coin.id == coinId || coin.symbol.toLowerCase() == coinId.toLowerCase(),
      orElse: () => _marketDataMap.values.first,
    );
  }

  // Cache işlemleri
  Future<void> _loadCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString('realtime_market_cache');

      if (cachedData != null) {
        final List<dynamic> data = json.decode(cachedData);
        for (final coinData in data) {
          final marketData = MarketData.fromJson(coinData);
          _marketDataMap[marketData.symbol.toUpperCase()] = marketData;
        }
      }
    } catch (e) {
      // Cache yükleme hatası
    }
  }

  // Bağlantı durumu kontrolü
  Future<bool> checkConnection() async {
    return _isConnected;
  }

  // Manuel yenileme
  Future<void> forceRefresh() async {
    await _loadInitialData();
  }

  // Temizlik
  void dispose() {
    _priceUpdateTimer?.cancel();
    _marketDataController.close();
    _connectionController.close();
    _priceChangeController.close();
  }
}
